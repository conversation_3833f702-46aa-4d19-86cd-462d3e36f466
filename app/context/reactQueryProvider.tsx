import { hydrate, QueryClient, QueryClientProvider } from "@tanstack/react-query";
import * as React from "react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDehydratedState } from "use-dehydrated-state";

export type ReactQueryProviderProps = React.PropsWithChildren<object>;

export const QueryKey = {
  CMS: "cms",
  PRODUCT_LIST: "marketplace_product_status",
  LOGIN: "login",
  THEME: "theme",
  PROJECT_LIST: "projectList",
  MERCHANT_DATA: "merchant_data",
  PROJECT_DETAIL: "projectDetail",
  PROJECT_DELETE: "projectDelete",
  CATEGORY_TEMPLATE: "category_template",
  CATEGORY_DATA: "category-data",
  UNIT_DATA: "unit-data",
  MERCHANT_SECTION_DATA: "merchant-section-data",
  UPDATE_MERCHANT_SECTION_DATA: "update-merchant-section-data",
  SUBMIT_ONBAORDING: "submit-onboarding",
  TEAM_MEMBERS: "team-members",
};

export const ReactQueryProvider = ({ children }: ReactQueryProviderProps) => {
  const [queryClient] = useState(() => new QueryClient());

  const dehydratedState = useDehydratedState();

  const { i18n } = useTranslation();

  React.useEffect(() => {
    if (dehydratedState) {
      hydrate(queryClient, dehydratedState);
    }
  }, [dehydratedState, queryClient]);

  React.useEffect(() => {
    queryClient.invalidateQueries({ queryKey: [QueryKey.CMS] });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [i18n.resolvedLanguage]);

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};
