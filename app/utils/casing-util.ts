export function snakeToCapitalizedWords(input: string): string {
  return input
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

export function snakeToCapital(input: string): string {
  return input
    .split("_")
    .map((word) => word.toUpperCase())
    .join(" ");
}

export function toTitle(input: string): string {
  return input
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}
