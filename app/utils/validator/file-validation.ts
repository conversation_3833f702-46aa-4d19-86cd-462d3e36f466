import { z } from "zod";

interface FileValidationOptions {
  allowedTypes?: string[];
  maxSizeInMB?: number;
  minFiles?: number;
  maxFiles?: number;
}

export function buildFileSchemaValidator(options: FileValidationOptions = {}) {
  const {
    allowedTypes = ["image/jpeg", "image/png"],
    maxSizeInMB = 5,
    minFiles,
    maxFiles,
  } = options;

  const maxSize = maxSizeInMB * 1024 * 1024;

  // Schema for previously uploaded file reference
  const existingFileSchema = z
    .object({
      url: z.string().url(),
      name: z.string().optional(),
      size: z.number().optional(),
      id: z.number().optional(),
      order: z.number().optional(),
    })
    .passthrough();

  // Schema for newly uploaded File object
  const newFileSchema = z
    .custom<File>((f) => f instanceof File, {
      message: "Invalid file upload",
    })
    .refine((file) => file.size <= maxSize, {
      message: `File must be under ${maxSizeInMB}MB`,
    })
    .refine((file) => allowedTypes.includes(file.type), {
      message: `Allowed types: ${allowedTypes.join(", ")}`,
    });

  // Accept either new or existing file
  const fileItemSchema = z.union([existingFileSchema, newFileSchema]);

  // Array of files with optional min/max validations
  let fileArraySchema = z.array(fileItemSchema);

  if (minFiles !== undefined) {
    fileArraySchema = fileArraySchema.min(minFiles, `At least ${minFiles} files required`);
  }

  if (maxFiles !== undefined) {
    fileArraySchema = fileArraySchema.max(maxFiles, `At most ${maxFiles} files allowed`);
  }

  return fileArraySchema;
}
