export const formatAsFormData = (
  file: File,
  {
    assetOrder,
    asset_id,
    slug,
    attribute_type,
  }: {
    asset_id?: string;
    assetOrder?: number | string;
    assetDes?: string;
    asset_type?: string;
    slug: string;
    attribute_type: string;
  },
) => {
  const defaultType = "DEFAULT";

  const assetTypeMap = {
    UPLOAD_FILE: "IMAGE",
    DEFAULT: "FILE",
    UPLOAD: "FILE",
    GALLERY: "IMAGE",
  };

  const formData = new FormData();
  formData.append("file", file);
  formData.append("title", file.name || "Untitled");
  formData.append(
    "asset_type",
    assetTypeMap[(attribute_type ?? defaultType) as keyof typeof assetTypeMap],
  );

  if (slug) {
    formData.append("attribute_slug", slug);
  }

  if (asset_id) {
    formData.append("asset_category_id", String(asset_id));
  }
  if (assetOrder) {
    formData.append("order", String(assetOrder));
  }

  return formData;
};
