type Field = {
  id: number;
  name: string;
  slug: string;
  attribute_type: string;
  parent_attribute_type?: string;
  parent_section_attribute_type?: string;
  sub_section?: string;
  meta?: Record<string, any>;
  [key: string]: any;
};

type Section = {
  section: string;
  fields: Field[];
};

type GroupedField = {
  attribute_type: string;
  section: string;
  child: Field[];
  [key: string]: any;
};

export function groupFieldsByParentType(inputArray: Section[]): Section[] {
  const transformedMap: Record<string, GroupedField> = {};
  const result: Section[] = [];

  inputArray.forEach(({ section, fields }) => {
    const normalFields: Field[] = [];

    fields.forEach((field) => {
      const parentType = field?.meta?.section?.parent_section_attribute_type;

      if (parentType && typeof parentType === "string") {
        const key = `${section}__${parentType}`;

        if (!transformedMap[key]) {
          transformedMap[key] = {
            attribute_type: parentType,
            section,
            child: [],
            ...(field?.meta?.section ?? {}),
          };
        }

        transformedMap[key].child.push(field);
      } else {
        normalFields.push(field);
      }
    });

    const groupedFields = Object.values(transformedMap).filter((f) => f.section === section);

    result.push({
      section,
      fields: [...normalFields, ...groupedFields],
    });
  });

  return result;
}
