export const formatCurrency = (
  value: number | string,
  locale: string = "en-US",
  currency: string = "USD",
): string => {
  const number = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(number)) return "";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(number);
};

export const formatDateToDDMMYYYY = (date: Date): string => {
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

const defaultOptions = {
  currency: "SAR",
  locale: "ar-SA-u-nu-latn",
  numberOfDigits: 2,
};
export const formatMoney = (
  amount: number | any,
  options: {
    currency?: string;
    locale?: string;
    numberOfDigits?: number;
    usingCode?: boolean;
  } = {},
) => {
  if (amount && isNaN(amount)) return "NaN";

  const fmt = new Intl.NumberFormat(options.locale ?? defaultOptions.locale, {
    currency: options.currency ?? defaultOptions.currency,
    currencyDisplay: options.usingCode ? "code" : undefined,
    maximumFractionDigits: options.numberOfDigits ?? defaultOptions.numberOfDigits,
    minimumFractionDigits: options.numberOfDigits ?? defaultOptions.numberOfDigits,
  });
  return `` + ` ${fmt.format(amount)}`;
};

export const extractPagination = (url: string, total: number = 0) => {
  const params = url ? new URL(url).searchParams : new URLSearchParams();
  const limit = Number(params.get("limit")) || 20;
  const offset = Number(params.get("offset")) || 0;
  const activePage = Math.floor(offset / limit) || 1;
  const pageCount = total < limit ? 1 : Math.ceil(total / limit);

  return { limit, offset, activePage, pageCount };
}
