export enum ListingStatus {
  PENDING_REVIEW = "PENDING_REVIEW",
  REJECTED = "REJECTED",
  DRAFT = "DRAFT",
  APPROVED = "APPROVED",
}

export enum Visibility {
  VISIBLE = "VISIBLE",
  HIDDEN = "HIDDEN",
}

export enum ListingNavStates {
  BLOCK_EDIT = "BLOCK_EDIT",
}

export const statusVariantMap: Record<ListingStatus, { variant: string }> = {
  [ListingStatus.PENDING_REVIEW]: { variant: "warning" },
  [ListingStatus.REJECTED]: { variant: "danger" },
  [ListingStatus.DRAFT]: { variant: "info" },
  [ListingStatus.APPROVED]: { variant: "success" },
};

export const visibilityVariantMap: Record<Visibility, { variant: string }> = {
  [Visibility.VISIBLE]: { variant: "success" },
  [Visibility.HIDDEN]: { variant: "neutral" },
};
