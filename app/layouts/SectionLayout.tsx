import { css } from "@emotion/react";
import { AppTheme, RDS<PERSON>utton } from "@roshn/ui-kit";
import { ReactNode } from "react";

type SectionLayoutProps = {
  primarySection: () => ReactNode;
  secondarySection: () => ReactNode;
  navigation?: {
    handleNavigation: () => void;
    typography: string;
  };
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  primarySection: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
      flex: "0 0 70%",
      flexDirection: "column",
    }),

  secondarySection: (theme: AppTheme) =>
    css({
      flex: "0 0 30%",
      position: "sticky",
      top: theme.rds.dimension["400"],
      alignSelf: "flex-start",
    }),

  sectionArrangement: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),
};

export default function SectionLayout({
  primarySection,
  secondarySection,
  navigation,
}: SectionLayoutProps) {
  return (
    <div css={styles.wrapper}>
      {navigation && (
        <RDSButton
          css={styles.button}
          variant="tertiary"
          size="lg"
          text={navigation.typography}
          leadIcon="left_arrow"
          onClick={navigation.handleNavigation}
        />
      )}
      <div css={styles.sectionsWrapper}>
        <div css={[styles.primarySection, styles.sectionArrangement]}>{primarySection()}</div>
        <div css={[styles.secondarySection, styles.sectionArrangement]}>{secondarySection()}</div>
      </div>
    </div>
  );
}
