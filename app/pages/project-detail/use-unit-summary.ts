

const getRangeValue = (min: string, max: string, type: string) => {
  if (type === "sqm") {
    return min >= max ? `${min} sqm` : `${min} sqm - ${max} sqm`;
  } else {
    return min >= max ? min : `${min} - ${max}`;
  }
};

export default function useUnitSummaryData(productData: any) {
  if (!productData) return {};
  const analyticsData = productData?.analytics;

  return {
    priceRange: {
      label: "Price range",
      minPrice: analyticsData?.['price_range']?.min,
      maxPrice: analyticsData?.['price_range']?.max,
      value: getRangeValue(`${analyticsData?.['price_range']?.min}`, `${analyticsData?.['price_range']?.max}`, "price"),
    },
    bedroomRange: {
      label: "Bedrooms",
      value: getRangeValue(`${analyticsData?.['bedrooms_range']?.min}`, `${analyticsData?.['bedrooms_range']?.max}`, "bedroom"),
    },
    bathroomRange: {
      label: "Bathrooms",
      value: getRangeValue(`${analyticsData?.['bathroom_range']?.min}`, `${analyticsData?.['bathroom_range']?.max}`, "bathroom"),
    },
    areaRange: {
      label: "Plot area",
      value: getRangeValue(`${analyticsData?.['plot_area_range']?.min}`, `${analyticsData?.['plot_area_range']?.max}`, "sqm"),
    },
    propertyTypes: {
      label: "Property types",
      range: analyticsData?.property_types,
    },
    nearbyAmenities: {
      label: "Unit features",
      range: analyticsData?.unit_features,
    },
  };
}
