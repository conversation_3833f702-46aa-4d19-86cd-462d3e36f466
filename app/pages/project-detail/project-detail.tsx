import { css, useTheme } from "@emotion/react";
import { useNavigate, useParams } from "@remix-run/react";
import {
  AppTheme,
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RDSButton,
  RDSLinkButton,
  RDSModal,
  RDSPagination,
  RDSSwitch,
  RDSTagBasic,
  RDSTextInput,
  RDSTypography,
} from "@roshn/ui-kit";
import parse from "html-react-parser";
import { useEffect, useState } from "react";

import { BreadCrumbs } from "~/components/bread-crumbs/bread-crumbs";
import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import { ListingNavStates, ListingStatus, statusVariantMap } from "~/constants/project-tag-config";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import UnitListSection from "~/features/unit-list/unit-list-section";
import { useAppPath } from "~/hooks/use-app-path";
import { useMoneyFormatter } from "~/hooks/use-money-formatter";
import { useProductList } from "~/services/product/hooks/use-product-list";
import { QueryProductListParams } from "~/services/product/product";
import { useChangeVisibility } from "~/services/project/hooks/use-change-visiblity";
import { useDeleteProject } from "~/services/project/hooks/use-delete-project";
import { useProjectDetail } from "~/services/project/hooks/use-project";
import { AppPaths } from "~/utils/app-paths";
import { snakeToCapitalizedWords, toTitle } from "~/utils/casing-util";
import { extractPagination, formatDateToDDMMYYYY } from "~/utils/helper";
import { toDate } from "~/utils/to-date";

import useUnitSummaryData from "./use-unit-summary";

const Tree = createSvg(() => import("~/assets/icons/tree.svg"));
const Mosque = createSvg(() => import("~/assets/icons/mekka.svg"));
const Hospital = createSvg(() => import("~/assets/icons/hospital-building.svg"));
const Graduate = createSvg(() => import("~/assets/icons/graduate.svg"));
const File = createSvg(() => import("~/assets/icons/file.svg"));
const Image = createSvg(() => import("~/assets/icons/image-gallery.svg"));

export enum CategoryDisplayStatus {
  VISIBLE = "Visible",
  HIDDEN = "Hidden",
}

export enum CategoryVisibilityStatus {
  VISIBLE = "VISIBLE",
  HIDDEN = "HIDDEN",
}

export enum Appearance {
  Success = "success",
  Neutral = "neutral",
}

type ButtonConfig = {
  text: string;
  onClick: () => void;
};

const tagData = [
  { label: "All", state: "active" },
  { label: "Hidden", state: "default", status: "HIDDEN" },
  { label: "Visible", state: "default", status: "ACTIVE" },
];

const styles = {
  divider: (theme: AppTheme) =>
    css({
      height: "24px",
      border: `1px solid ${theme.rds.color.border.ui.secondary}`,
    }),

  tagsWrapper: css({ display: "flex", flexWrap: "wrap", gap: "1rem" }),

  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  additionalItemsWrapper: css({
    display: "flex",
    gap: "2rem",
    flexDirection: "column",
  }),

  additionalContent: css({
    display: "flex",
    flexDirection: "column",
    gap: "1.5rem",
    textWrap: "nowrap",
  }),

  additionalContentWrapper: css({
    display: "flex",
    flexDirection: "row",
    gap: "1rem",
  }),

  labelWrapper: css({
    minWidth: "10rem",
  }),

  summaryContent: css({
    display: "flex",
    gap: "1rem",
    textWrap: "nowrap",
    flexDirection: "column",
  }),

  attachments: css({ display: "flex", alignItems: "center", gap: "16px" }),

  attachmentItem: css({
    display: "flex",
    gap: "8px",
    justifyContent: "center",
    alignItems: "center",
  }),

  attachmentsTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["87"],
      fontWeight: 300,
      color: theme.rds.color.text.brand.primary.default,
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  detailWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  itemsWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  detailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.display.d5,
      ...theme.rds.brand.font.fontFamily.display,
      fontWeight: 300,
      color: theme.rds.color.text.ui.primary,
    }),

  detailSubHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md5,
      ...theme.rds.brand.font.fontFamily.body,
      color: theme.rds.color.text.ui.primary,
    }),

  license: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["100"],
      textDecoration: "underline",
    }),

  expectedStyles: (theme: AppTheme) =>
    css({
      display: "inline-block",
      paddingInline: "0.5rem",
      color: theme.rds.color.text.ui.tertiary,
    }),

  detailDescription: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.lg,
      fontWeight: 400,
      color: theme.rds.color.text.ui.primary,
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      fontWeight: 500,
      color: theme.rds.color.text.ui.tertiary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  detailSubHeadingWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["400"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  additionalDetailHeading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      ...theme.rds.font.fontSize["150"],
      color: theme.rds.color.text.ui.primary,
      whiteSpace: "nowrap",
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),

  additionalDetailContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "center",
      gap: theme.rds.dimension["200"],
      width: "100%",
    }),

  dividerLine: (theme: AppTheme) =>
    css({
      flex: 1,
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.tertiary,
    }),

  image: css({
    width: "60px",
    height: "60px",
  }),
};

const tagOptions = [
  {
    label: "Public Park",
    value: "park",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Mosque",
    value: "mosque",
    disabled: false,
    leadIcon: <Mosque />,
  },
  {
    label: "Health Centre",
    value: "health-center",
    disabled: false,
    leadIcon: <Hospital />,
  },

  {
    label: "Sports ground",
    value: "sport-ground",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "School",
    value: "school",
    disabled: false,
    leadIcon: <Graduate />,
  },
  {
    label: "Kindergarten",
    value: "kindergarten",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Retail centres",
    value: "retail-center",
    disabled: false,
    leadIcon: <Tree />,
  },
  {
    label: "Dining & Entertainment",
    value: "dinning-and-entertainment",
    disabled: false,
    leadIcon: <Mosque />,
  },
];

export default function ProjectDetail() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const { id = "" } = useParams();
  const theme = useTheme();
  const formatMoney = useMoneyFormatter();

  const { mutateAsync } = useChangeVisibility({});

  const [showDeletion, setDeletion] = useState(false);
  const [delModalInput, setDelModalInp] = useState("");
  const { data, isFetching } = useProjectDetail(id);

  const [activeTag, setActiveTag] = useState("All");
  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });

  const [pagination, setPagination] = useState({
    limit: 20,
    offset: 0,
    activePage: 1,
    pageCount: 0,
  });

  const getProdParamStatus = () => {
    return {
      status: tagData.find((tag) => tag.label === activeTag)?.status,
    }
  };

  const productListParams: QueryProductListParams = {
    ...getProdParamStatus(),
    search: searchParam.search,
    marketplace_merchant_category: id,
    limit: pagination.limit,
    offset: (pagination.activePage - 1) * pagination.limit,
  };

  const {
    data: productData,
    isFetching: isUnitFetching,
    refetch,
  } = useProductList(productListParams);

  const summaryData = useUnitSummaryData(data);

  useEffect(() => {
    if (productData?.next || productData?.count !== undefined) {
      const newPagination = extractPagination(productData.next, productData.count);
      setPagination(newPagination);
    }
  }, [productData]);

  const [visible, setVisible] = useState(data?.status?.customer_visibility_status === "VISIBLE");

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.project));
  };

  const handleProjectEditNav = (stateConfig = "") => {
    navigate(
      generateAppPath(AppPaths.editProject, {
        id,
      }),
      { state: stateConfig ?? "" },
    );
  };

  const deleteProject = useDeleteProject({
    onSuccess: () => {
      navigate(generateAppPath(AppPaths.project));
    },
  });

  const handleProjectDeletion = () => {
    deleteProject.mutateAsync({ id });
  };

  const availableAmenities = data?.custom_attributes?.["nearby_amenities"] || [];

  const getVisibilityStatus = () => ({
    label:
      data?.status?.customer_visibility_status === CategoryVisibilityStatus.VISIBLE
        ? CategoryDisplayStatus.VISIBLE
        : CategoryDisplayStatus.HIDDEN,
    appearance:
      data?.status?.customer_visibility_status === CategoryVisibilityStatus.VISIBLE
        ? Appearance.Success
        : Appearance.Neutral,
  });

  const projectLogo = data?.assets_grouped_by_category?.logo?.assets?.at(-1)?.url;

  const handlePagination = (setPage: number) => {
    setPagination((prev) => ({ ...prev, activePage: setPage }));
  };

  const defaultListingBtnConfig = {
    text: "CONTINUE EDITING",
    onClick: () => handleProjectEditNav(),
  };

  const listingStatusButtonMap: Record<ListingStatus, ButtonConfig> = {
    [ListingStatus.DRAFT]: defaultListingBtnConfig,
    [ListingStatus.PENDING_REVIEW]: {
      text: "REVIEW SUBMISSION",
      onClick: () => handleProjectEditNav(ListingNavStates.BLOCK_EDIT),
    },
    [ListingStatus.APPROVED]: defaultListingBtnConfig,
    [ListingStatus.REJECTED]: defaultListingBtnConfig,
  };

  return (
    <div css={styles.wrapper}>
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Confirm project deletion",
            type: "centred",
          }}
          isOpen={showDeletion}
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={handleProjectDeletion}
                text="DELETE PROJECT"
                key="delete"
                disabled={delModalInput !== "DELETE"}
              />,
              <RDSButton
                variant="secondary"
                onClick={() => setDeletion(false)}
                text="CANCEL"
                key="cancel"
              />,
            ],
            direction: "vertical",
          }}
          content={
            <RDSTextInput
              value={delModalInput}
              onChange={(e) => setDelModalInp(e.target.value)}
              placeholder="DELETE"
            />
          }
          description={
            (
              <>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  Deleting this project is permanent. We recommend saving it as a draft if you may
                  need it later.
                </RDSTypography>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  To confirm deletion, please type DELETE in the box below.
                </RDSTypography>
              </>
            ) as any
          }
          showContent
          showDescription
        />
      </div>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to project list"
        leadIcon="left_arrow"
        onClick={handleProjectNav}
      />
      {isFetching || isUnitFetching ? (
        <div
          css={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh",
            width: "85vw",
          }}
        >
          <RoshnContainerLoader />
        </div>
      ) : (
        <>
          <div css={styles.sectionsWrapper}>
            <div css={styles.detailWrapper}>
              <div css={styles.itemsWrapper}>
                <BreadCrumbs
                  items={[
                    { label: "Project list", action: handleProjectNav },
                    { label: "Project details" },
                  ]}
                />
                <div css={styles.detailSubHeadingWrapper}>
                  {data?.title && (
                    <RDSTypography css={styles.detailHeading}>{data?.title}</RDSTypography>
                  )}
                  <img
                    src={
                      projectLogo ??
                      "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg"
                    }
                    css={styles.image}
                    alt="Roshn Logo"
                  />
                </div>
                <div css={styles.detailSubHeadingWrapper}>
                  {data?.custom_attributes?.city && (
                    <RDSTypography css={styles.detailSubHeading}>
                      {`${toTitle(data?.custom_attributes?.city)}`}
                      {data?.custom_attributes["handover_date"] && (
                        <span css={styles.expectedStyles}>
                          (Expected for:{" "}
                          {formatDateToDDMMYYYY(new Date(data?.custom_attributes["handover_date"]))}
                          )
                        </span>
                      )}
                    </RDSTypography>
                  )}
                  {data?.custom_attributes?.["rega_license_number"] && (
                    <RDSTypography css={[styles.detailSubHeading, styles.license]}>
                      {`REGA license: ${data?.custom_attributes["rega_license_number"]}`}
                    </RDSTypography>
                  )}
                </div>
                {data?.custom_attributes?.["description_en"] && (
                  <RDSTypography css={styles.detailDescription}>
                    {parse(data?.custom_attributes?.["description_en"])}
                  </RDSTypography>
                )}
              </div>
              <div css={styles.itemsWrapper}>
                <div css={styles.additionalDetailContainer}>
                  <RDSTypography css={styles.additionalDetailHeading}>
                    Additional details
                  </RDSTypography>
                  <div css={styles.dividerLine} />
                </div>
                <div css={styles.additionalItemsWrapper}>
                  <div css={styles.additionalContent}>
                    <div css={styles.additionalContentWrapper}>
                      <div css={styles.labelWrapper}>
                        <RDSTypography tag={"span"} fontName={theme?.rds?.typographies.body.md}>
                          Attachments:
                        </RDSTypography>
                      </div>
                      <div css={styles.attachments}>
                        {Object.entries(data?.assets_grouped_by_category).map(
                          ([key, value], index) =>
                            key !== "logo" && (
                              <>
                                <div css={styles.attachmentItem}>
                                  <RDSLinkButton
                                    href={value?.assets?.[0]?.url}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                    text={value?.assets?.[0]?.asset_category}
                                    leadIcon={
                                      <RDSAssetWrapper>
                                        {key === "brochures" ? <File /> : <Image />}
                                      </RDSAssetWrapper>
                                    }
                                  />
                                </div>
                                {index <= 2 && <div css={styles.divider} />}
                              </>
                            ),
                        )}
                      </div>
                    </div>
                  </div>
                  <div css={styles.additionalContent}>
                    <div css={styles.additionalContentWrapper}>
                      <div css={styles.labelWrapper}>
                        {availableAmenities.length > 0 && (
                          <RDSTypography tag={"span"} fontName={theme?.rds?.typographies.body.md}>
                            Nearby amenities:
                          </RDSTypography>
                        )}
                      </div>
                      <div css={styles.tagsWrapper}>
                        {tagOptions
                          .filter((value) => availableAmenities.includes(value.value))
                          .map(({ leadIcon, label }, index) => (
                            <RDSTagBasic
                              leadIcon={leadIcon}
                              key={index}
                              label={label}
                              appearance="neutral"
                            />
                          ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {productData?.results.length > 0 && (
                <div css={styles.itemsWrapper}>
                  <div css={styles.additionalDetailContainer}>
                    <RDSTypography css={styles.additionalDetailHeading}>
                      Unit's summary
                    </RDSTypography>
                    <div css={styles.dividerLine} />
                  </div>
                  <div css={styles.additionalItemsWrapper}>
                    <div css={styles.additionalContent}>
                      {Object.entries(summaryData).map(([key, value]) => (
                        <div css={styles.additionalContentWrapper}>
                          <div css={styles.labelWrapper}>
                            <RDSTypography
                              tag={"span"}
                              key={key}
                              fontName={theme?.rds?.typographies.body.md}
                            >
                              {value?.label}:
                            </RDSTypography>
                          </div>
                          <div css={styles.tagsWrapper}>
                            {value?.value && (
                              <RDSTagBasic
                                key={key}
                                label={
                                  value.label === "Price range"
                                    ? productData?.results?.length === 1
                                      ? formatMoney(value?.minPrice, {
                                          numberOfDigits: 0,
                                          usingCode: false,
                                        })
                                      : `${formatMoney(value?.minPrice, {
                                          numberOfDigits: 0,
                                          usingCode: false,
                                        })} - ${formatMoney(value?.maxPrice, {
                                          numberOfDigits: 0,
                                          usingCode: false,
                                        })}`
                                    : value?.value
                                }
                                appearance="neutral"
                              />
                            )}
                            {value?.range &&
                              value.range
                                .filter((inner) => inner !== null)
                                .map((label: string, index: number) => (
                                  <RDSTagBasic
                                    key={index}
                                    label={snakeToCapitalizedWords(label)}
                                    appearance="neutral"
                                  />
                                ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div css={[styles.sectionLayout, styles.infoSections]}>
              <Section heading="VISIBILITY" tag={getVisibilityStatus()}>
                <RDSTypography css={styles.actionsHeadingText}>
                  The current approved version remains visible to customers until new changes are
                  approved.
                </RDSTypography>

                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Approved on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>{toDate(data?.updated_date)}</RDSTypography>
                </div>

                <div css={styles.infoTypoWrapper}>
                  <RDSTypography fontName={theme?.rds?.typographies.label.md}>
                    Show to Customers
                  </RDSTypography>
                  <RDSSwitch
                    checked={visible}
                    disabled={data?.status?.approval_status !== "APPROVED"}
                    onCheckedChange={(e: boolean) => {
                      const visibility = e ? "VISIBLE" : "HIDDEN";

                      mutateAsync({
                        categoryId: id as any,
                        marketSlug: "brokerage",
                        payload: {
                          visibility_status: visibility,
                        },
                      }).then(() => {
                        setVisible(e);
                      });
                    }}
                  />
                </div>
              </Section>
              <Section
                heading="LISTING STATUS"
                tag={{
                  label: data?.status?.approval_status,
                  appearance: statusVariantMap[data?.status?.approval_status].variant,
                }}
              >
                <div css={styles.internalWrapper}>
                  <RDSTypography css={styles.actionsHeadingText}>
                    Please complete all required fields before submitting for review.
                  </RDSTypography>
                  <RDSButton
                    variant="primary"
                    size="lg"
                    {...listingStatusButtonMap[
                      (data?.status?.approval_status ??
                        ListingStatus.DRAFT) as keyof typeof ListingStatus
                    ]}
                  />
                </div>
              </Section>
              <Section heading="DELETE PROJECT">
                <div css={styles.internalWrapper}>
                  <RDSTypography css={styles.actionsHeadingText}>
                    Deleting this project is permanent. Consider hiding it from public visibility
                    instead.
                  </RDSTypography>
                  <RDSButton
                    variant="secondary"
                    size="lg"
                    text="DELETE PROJECT"
                    onClick={() => setDeletion(true)}
                  />
                </div>
              </Section>
            </div>
          </div>
          <UnitListSection
            tagData={tagData}
            activeTag={activeTag}
            setActiveTag={setActiveTag}
            search={search}
            setSearch={setSearch}
            searchParam={searchParam}
            setSearchParam={setSearchParam}
            productList={productData}
            refetch={refetch}
          />
          {productData?.results.length > 0 && (
            <RDSPagination
              pageCount={pagination.pageCount}
              activePage={pagination.activePage}
              onPageChange={handlePagination}
            />
          )}
        </>
      )}
    </div>
  );
}
