import { css } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDS<PERSON>utton, RDSTypography } from "@roshn/ui-kit";
import { useState } from "react";

import { createSvg } from "~/components/svgs";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { useAppPath } from "~/hooks/use-app-path";
import { useMerchantData } from "~/services/merchant-data";
import { AppPaths } from "~/utils/app-paths";

export const ProfileIcon = createSvg(
  () => import("~/assets/icons/profile.svg") as unknown as Promise<{ ReactComponent: any }>,
);

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      justifyContent: "center",
      height: "100vh",
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      gap: theme.rds.dimension["300"],
    }),

  emptyWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      width: "100%",
      alignItems: "center",
    }),

  headingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h3,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.lg,
      color: theme.rds.color.text.ui.secondary,
      textAlign: "center",
    }),

  button: css({
    height: "48px",
    width: "480px",
    marginInline: "auto",
  }),

  icon: css({
    height: "80px",
    width: "80px",
  }),

  secondaryButton: css({
    height: "48px",
    width: "480px",
    marginTop: "16px",
    marginInline: "auto",
  }),

  loaderWrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "100vh",
  }),
};

export default function ProfileNav() {
  const [type, setType] = useState<"new-profile" | "old-profile">("old-profile");

  const generateAppPath = useAppPath();
  const navigate = useNavigate();

  const handleNavigation = () => navigate(generateAppPath(AppPaths.publicProfile));

  const { data, isLoading } = useMerchantData();

  if (isLoading) {
    return (
      <div css={styles.loaderWrapper}>
        <RoshnContainerLoader />
      </div>
    );
  }

  return (
    <div css={styles.wrapper}>
      <ProfileIcon css={styles.icon} />
      <div css={styles.emptyWrapper}>
        <RDSTypography css={styles.headingText}>
          {type === "new-profile"
            ? "Let’s begin setting up your public profile"
            : "Preview & edit your public profile!"}
        </RDSTypography>
        <RDSTypography css={styles.descriptionText}>
          Your public profile is your gateway to connecting with potential customers. It allows them
          to learn more about you, browse your projects, and explore the properties you currently
          offer.
        </RDSTypography>
      </div>
      {type === "new-profile" ? (
        <RDSButton
          css={styles.button}
          variant="primary"
          onClick={handleNavigation}
          text="START NOW"
          key="continue"
        />
      ) : (
        <div>
          <RDSButton
            css={styles.button}
            variant="primary"
            onClick={handleNavigation}
            text="EDIT PROFILE"
          />
          <RDSButton
            css={styles.secondaryButton}
            variant="secondary"
            text="PREVIEW PROFILE"
            href={data?.store_preview_url}
            target="_blank"
          />
        </div>
      )}
    </div>
  );
}
