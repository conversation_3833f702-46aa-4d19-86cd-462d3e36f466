import { css, useTheme } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDSButton, RDSTypography } from "@roshn/ui-kit";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { BrandWrapper } from "~/components/brand-wrapper/brand-wrapper";
import { Input } from "~/components/form-components/input/input";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { useLogin } from "~/hooks/use-login";
import { StoreService } from "~/services/store-service/store-service-impl";
import { AppPaths } from "~/utils/app-paths";

const styles = {
  wrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100vw",
    height: "100vh",
  }),

  authWrapper: (theme: AppTheme) =>
    css({
      padding: theme?.rds.dimension["400"],
      backgroundColor: theme?.rds.color.background.ui.primary.default,
      minWidth: theme?.rds.dimension["4000"],
      width: "640px",
      display: "flex",
      flexDirection: "column",
      alignItems: "center",
      gap: theme?.rds.dimension["300"],
      textAlign: "center",
    }),

  logo: css({
    width: "130px",
    height: "40px",
    marginBlock: "42px",
  }),

  welcomeText: (theme: AppTheme) =>
    css({
      ...theme?.rds.typographies.heading.emphasis.h4,
      fontWeight: 500,
      color: theme?.rds.color.text.ui.primary.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme?.rds.typographies.body.md,
      color: theme?.rds.color.text.ui.primary.secondary,
      marginBlockEnd: theme?.rds.dimension["200"],
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension["200"],
      width: "100%",
    }),

  forgotPasswordText: (theme: AppTheme) =>
    css({
      ...theme?.rds.typographies.label.lg,
      color: theme?.rds.color.text.brand.primary.default,
      cursor: "pointer",
    }),
};

const schema = z.object({
  username: z.string().email({ message: "Invalid email address" }).trim(),
  password: z.string().min(1, { message: "Password must be at least 1 characters" }),
});

export default function LoginPage() {
  const { handleSubmit, control, setError } = useForm({
    resolver: zodResolver(schema),
    mode: "onSubmit",
    defaultValues: {
      password: "",
      username: "",
    },
  });

  const navigate = useNavigate();
  const theme = useTheme() as AppTheme;
  const storeService = useInjection(StoreService);

  const { mutateAsync, isPending } = useLogin({
    onError: (data) => {
      const message =
        data?.response?.data?.extra_info?.non_field_errors?.[0] ??
        "Incorrect username or password.";

      setError("password", { message });
    },
  });

  const generatePath = useAppPath();

  const handleLogin = async (args: { username: string; password: string }) => {
    try {
      await mutateAsync(args);
      storeService.state.setSignedIn({
        signedIn: true,
        sub: "",
        updatedPhoneNumber: false,
        userName: "",
      });
      navigate(generatePath(AppPaths.dashboard));
    } catch (err) {
      console.error("Failed to login", err);
    }
  };

  const handleForgotPassNav = () => {
    navigate(generatePath(AppPaths.forgotPassword));
  };

  return (
    <form
      css={styles.wrapper}
      onSubmit={handleSubmit((args) => {
        handleLogin(args);
      })}
    >
      <BrandWrapper>
        <div css={styles.sectionWrapper}>
          <RDSTypography css={styles.welcomeText}>Welcome to ROSHN Sellers!</RDSTypography>
          <RDSTypography css={styles.descriptionText}>
            Manage your product inventory, track performance, and unlock insights—all in one
            dashboard.
          </RDSTypography>
        </div>
        <div css={styles.sectionWrapper}>
          <Input
            control={control}
            name="username"
            data-testid="email"
            label="E-mail"
            placeholder="Enter your email"
          />
          <Input
            control={control}
            name="password"
            label="Password"
            data-testid="password"
            placeholder="Enter your password"
            type="password"
          />
        </div>
        <div css={styles.sectionWrapper}>
          <RDSButton
            css={{ textTransform: "none" }}
            variant="primary"
            size="lg"
            data-testid="login"
            text="Log in"
            type="submit"
            loading={isPending}
          />
          <RDSButton
            css={{ textTransform: "none" }}
            variant="secondary"
            size="lg"
            text="Start enrollment now"
            type="button"
            onClick={() => {
              navigate(generatePath(AppPaths.onboarding));
            }}
          />
          <RDSTypography onClick={handleForgotPassNav} css={styles.forgotPasswordText}>
            Forgot password? Reset password
          </RDSTypography>
        </div>
      </BrandWrapper>
    </form>
  );
}
