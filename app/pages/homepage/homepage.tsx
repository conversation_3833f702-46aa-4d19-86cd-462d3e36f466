import { css, useTheme } from "@emotion/react";
import {
  RDSTagInteractive,
  RDSDashboardTab,
  RDSTypography,
  AppTheme,
  RDSTable,
  RDSEmptyState,
  RDSPagination,
} from "@roshn/ui-kit";

import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { useInterestList } from "~/services/product/hooks/use-product-list";

const styles = {
  emptyStateWrapper: (theme: AppTheme) =>
    css({
      marginBlock: theme.rds.dimension["500"],
      justifyContent: "center",
      height: "fit-content",
      "& p": {
        margin: 0,
      },
      ".roshn-boilerplate-fe-1ex6y7e": {
        width: "220px",
      },
    }),
  container: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension[200],
      padding: `${theme?.rds.dimension["400"]} ${theme?.rds.dimension["600"]}`,
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
      minHeight: "100vh",
      width: "100%",
    }),
  header: (theme: AppTheme) =>
    css({
      marginBottom: theme?.rds.dimension["400"],
    }),
  metricsSection: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension["300"],
    }),
  tagsRow: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme?.rds.dimension["200"],
    }),
  metricsGrid: (theme: AppTheme) =>
    css({
      display: "grid",
      gridTemplateColumns: "repeat(4, 1fr)",
      gap: theme?.rds.dimension["200"],
      marginBottom: theme?.rds.dimension["400"],
    }),
  insightsSection: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme?.rds.dimension["300"],
    }),
  insightsList: css({
    display: "flex",
    flexDirection: "column",
  }),
};

const timeFilters = [
  { label: "This week", state: "active" },
  { label: "This month", state: "default" },
  { label: "Last 6 months", state: "default" },
  { label: "This year", state: "default" },
  { label: "All time", state: "default" },
];

const metrics = [
  {
    title: "Gross Merchandise Value (GMV)",
    currency: "SAR",
    amount: "7,640,000",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "33%",
    percentageChangeType: "increase",
  },
  {
    title: "Number of views on properties",
    amount: "1223",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "2% ",
    percentageChangeType: "increase",
  },
  {
    title: "Engagement Rate (Clicks per view)",
    amount: "73%",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "6%",
    percentageChangeType: "decrease",
  },
  {
    title: "View to Interest Conversion",
    amount: "40%",
    tooltipContent: {
      label: "hello",
      description: "world",
      withLink: false,
      direction: "top",
    },
    percentageChange: "7%",
    percentageChangeType: "increase",
  },
];

const _mockData = [
  {
    id: 4077,
    order_id: 101,
    payment_type: "marketplace_pay",
    payment_type_display: "Marketplace Pay",
    fulfillment_method: "PROPERTY_TRANSACTIONS",
    fulfillment_method_display: "Property Transactions",
    payment_status_display: "Postponed for later",
    payment_status: "POSTPONED_FOR_LATER",
    payment_phase: null,
    payment_state: null,
    fulfillment_status: "AWAITING_USER_CONFIRMATION",
    fulfillment_status_display: "Awaiting User Confirmation",
    final_payment_statuses: ["REFUNDED", "REVERSAL_COMPLETED", "REFUNDED"],
    final_fulfillment_statuses: [
      "REJECTED",
      "FAILED",
      "CANCELLED_BY_SELLER",
      "CANCELLED_BY_CUSTOMER",
      "COMPLETED",
      "EXPIRED",
      "RETURNED",
      "RETURN_REQUESTED",
      "PARTIALLY_RETURNED",
      "REFUNDED_BY_SELLER",
    ],
    customer: {
      id: 649,
      first_name: "Mohd",
      last_name: "H",
      email: "<EMAIL>",
      phone_number: "",
      customer_type: {
        value: "RECURRING",
        label: "Recurring",
      },
      is_guest_customer: false,
      orders: {
        total: 2,
        sum: "0",
      },
      created_date: "2025-07-08T13:45:20.832148Z",
    },
    customer_first_name: "Mohd",
    customer_last_name: "H",
    customer_email: "<EMAIL>",
    customer_phone: "+966555283599",
    number_of_items: 1,
    amount: "1221671.00",
    shipping_line: null,
    content_images: [
      "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/ecommerce/products/80d51ce8-3178-476e-8196-8b2a4093b5e8.webp",
    ],
    newly_added_contents: 0,
    created_date: "2025-07-08T13:45:21.392178Z",
    is_archived: false,
    is_scheduled: false,
    scheduled_start_date: null,
    scheduled_end_date: null,
    is_custom_payment: false,
    is_demo_order: false,
    staff_assistance: [],
    source: "ROSHN Brokerage",
  },
];

export default function LandingPage() {
  const theme = useTheme() as AppTheme;
  const { data, isFetching } = useInterestList();
  const interestList = data?.results || [];

  const mockData = [...[], ...interestList].map((interest: any, index: number) => ({
    id: `row-${index}`,
    column1: { dataValue: new Date(interest?.created_date).toLocaleDateString("en-US") },
    column2: {
      dataValue:
        `${interest?.customer_first_name}${interest?.customer_last_name}` === "BahaaH"
          ? "Mohammed Alhazmi"
          : `${interest?.customer_first_name} ${interest?.customer_last_name}`,
    },
    column3: { dataValue: `BEYOND-${interest?.id}` },
    column4: { dataValue: interest?.fulfillment_status.replace(/_/g, " ") },
  }));

  const tableData = {
    title: "Interest",
    columns: [
      {
        id: "checkbox",
        header: "",
        accessor: "id",
        type: "checkbox",
      },
      {
        id: "column1",
        header: "Date",
        accessor: "column1",
        type: "text",
      },
      {
        id: "column2",
        header: "Customer",
        accessor: "column2",
        type: "text",
      },
      {
        id: "column3",
        header: "Unit",
        accessor: "column3",
        type: "text",
      },
      {
        id: "column4",
        header: "Status",
        accessor: "column4",
        type: "tag",
      },
      {
        id: "actions",
        header: "Actions",
        accessor: "id",
        type: "action",
      },
    ],
    data: mockData,
  };

  return (
    <div css={styles.container}>
      <RDSTypography fontName={theme?.rds?.typographies.display.d5} css={styles.header}>
        Dashboard
      </RDSTypography>
      <section css={styles.metricsSection}>
        <RDSTypography fontName={theme?.rds?.typographies.heading.h4}>Metrics</RDSTypography>
        <div css={styles.tagsRow}>
          {timeFilters.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              label={tag.label}
              state={tag.state as "default" | "active"}
              // Add onClick handler as needed
            />
          ))}
        </div>
        <div css={styles.metricsGrid}>
          {metrics.map((metric) => (
            <RDSDashboardTab
              key={metric.title}
              amount={metric.amount}
              title={metric.title}
              currency={metric.currency}
              tooltipContent={metric?.tooltipContent}
              percentageChange={metric.percentageChange}
              percentageChangeType={metric.percentageChangeType}
            />
          ))}
        </div>
        {isFetching ? (
          <RoshnContainerLoader />
        ) : true ? (
          <RDSTable
            title={tableData.title}
            description={tableData.description}
            columns={tableData.columns}
            data={tableData.data}
            pagination={false}
          />
        ) : (
          <div css={styles.emptyStateWrapper}>
            <RDSEmptyState
              showMedia={false}
              title="You  haven't listed  any interest"
              size="sm"
              body="Projects you create will appear here. Add new projects now to fill up your list."
            />
          </div>
        )}
      </section>
    </div>
  );
}
