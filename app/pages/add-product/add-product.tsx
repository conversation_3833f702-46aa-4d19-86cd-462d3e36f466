import { useParams } from "@remix-run/react";
import { useMemo } from "react";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import ProductForm from "~/features/unit/unit-form";
import WithLoader from "~/hoc/withLoader";
import { useGetTemplate } from "~/services/project/hooks/use-get-template";
import { useProjectDetail } from "~/services/project/hooks/use-project";

export default function AddProductPage() {
  const { id } = useParams();
  const { data } = useGetTemplate({
    id: 1,
  });

  const productFields = useMemo(() => {
    const sections = data?.product_attributes ?? [];
    return sections.flatMap((section: any) => section.fields || []);
  }, [data]);
  const { isLoading } = useProjectDetail(id);
  return (
    <WithLoader
      loading={isLoading}
      LoaderComponent={RoshnContainerLoader}
      Component={ProductForm}
      componentProps={{ productFields, schema: data }}
    />
  );
}
