import { useParams } from "@remix-run/react";
import { useEffect, useState } from "react";

import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import ProjectForm from "~/features/project/project-form";
import WithLoader from "~/hoc/withLoader";
import { useCategoryData } from "~/services/project/hooks/use-category-data";
import { toDate } from "~/utils/to-date";

async function createFilesFromAssetsByCategory(data: Record<string, any>) {
  const result: Record<string, File> = {};

  await Promise.all(
    Object.entries(data ?? {}).map(async ([, value]) => {
      const asset = value.assets?.[0];
      if (!asset?.url || !asset?.filename) return;

      try {
        const blob = await fetch(asset.url);

        result[asset.asset_category_slug] = new File([blob], asset.filename, {
          type: blob.type,
        });
      } catch (err) {
        console.warn("Skipping asset due to error:", err);
      }
    }),
  );

  return result;
}

export default function EditProject() {
  const { id } = useParams();

  const [assetsLoading, setAssetsLoading] = useState(true);

  const [assets, setAssets] = useState({});

  const { data, isLoading } = useCategoryData({ id: Number(id) });

  const defaultValues = structuredClone(data?.custom_attributes ?? {});

  const [meta, setMeta] = useState(null);

  useEffect(() => {
    if (data?.assets_grouped_by_category) {
      createFilesFromAssetsByCategory(data.assets_grouped_by_category).then((result) => {
        setAssets(result);
        setAssetsLoading(false);
      });
    }
  }, [data?.assets_grouped_by_category]);

  useEffect(() => {
    setMeta({
      createdOn: toDate(data?.created_date),
      updatedOn: toDate(data?.updated_date),
      createdBy: "-",
      updatedBy: "-",
      status: data?.status?.approval_status ?? "DRAFT",
    });
  }, [data]);

  const isLogo = data?.assets_grouped_by_category?.logo?.assets?.length;

  return (
    <WithLoader
      loading={isLoading || assetsLoading}
      LoaderComponent={RoshnContainerLoader}
      Component={ProjectForm}
      componentProps={{
        defaultValues: {
          ...defaultValues,
          logo: [...(isLogo ? [data?.assets_grouped_by_category?.logo?.assets?.at(-1)] : [])],
          project_documents: assets,
        },
        editCategoryId: id,
        edit: true,
        metaData: meta,
      }}
    />
  );
}
