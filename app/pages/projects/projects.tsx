import { css, useTheme } from "@emotion/react";
import { ChevronRightIcon } from "@radix-ui/react-icons";
import { useNavigate } from "@remix-run/react";
import {
  RDSTable,
  RDSTagInteractive,
  RDSButton,
  RDSTypography,
  AppTheme,
  RDSSearchInput,
  RDSEmptyState,
  RDSPagination,
  Image,
  RDSLinkButton,
  RDSTagBasic,
} from "@roshn/ui-kit";
import { useEffect, useState } from "react";

import { statusVariantMap, visibilityVariantMap } from "~/constants/project-tag-config";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { useAppPath } from "~/hooks/use-app-path";
import { useProjectList } from "~/services/project/hooks/use-project";
import { QueryProjectListParams } from "~/services/project/project";
import { AppPaths } from "~/utils/app-paths";
import { snakeToCapitalizedWords } from "~/utils/casing-util";
import { extractPagination } from "~/utils/helper";

type VariantMapType = {
  [key: string]: { variant: string };
};

const tagData = [
  { label: "All", state: "active" },
  { label: "Drafted", state: "default", status: "DRAFT" },
  { label: "In review", state: "default", status: "PENDING_REVIEW" },
  { label: "Published", state: "default", status: "APPROVED" },
];

const getTableHeading = (heading: string, theme: AppTheme) => {
  return (
    <RDSTypography isBold fontName={theme?.rds?.typographies?.label?.emphasis?.md}>
      {heading}
    </RDSTypography>
  );
};

const tableData = (theme: AppTheme) => ({
  columns: [
    {
      id: "column1",
      header: getTableHeading("Projects", theme),
      accessor: "column1",
    },
    {
      id: "column2",
      header: getTableHeading("Units Sold", theme),
      accessor: "column2",
      type: "text",
    },
    {
      id: "column3",
      header: getTableHeading("Listing Status", theme),
      accessor: "column3",
      type: "tag",
    },
    {
      id: "column4",
      header: getTableHeading("Visibility", theme),
      accessor: "column4",
      type: "tag",
    },
    {
      id: "column5",
      header: getTableHeading("Actions", theme),
      accessor: "column5",
    },
  ],
});

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      padding: theme.rds.dimension["600"],
      background: theme?.rds?.color?.background?.brand?.secondary?.inverse?.default,
      minHeight: "100vh",
      gap: theme.rds.dimension["200"],
    }),
  emptyStateWrapper: (theme: AppTheme) =>
    css({
      margin: "auto",
      marginTop: "0",
      padding: `${theme?.rds.dimension[500]} 0`,
    }),
  headerContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),
  header: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      alignContent: "center",
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  leadImage: () =>
    css({
      width: "5.5rem",
      height: "1.75rem",
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),

  loaderWrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height: "50vh",
  }),
};

export default function ProjectsPage() {
  const theme = useTheme() as AppTheme;
  const generatePath = useAppPath();
  const navigate = useNavigate();

  const [pagination, setPagination] = useState({
    limit: 20,
    offset: 0,
    activePage: 1,
    pageCount: 0,
  });

  const [activeTag, setActiveTag] = useState("All");

  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });

  const getProjectStatus = () => {
    return {
      status: tagData.find((tag) => tag.label === activeTag)?.status,
    };
  };

  const projectListParams: QueryProjectListParams = {
    ...getProjectStatus(),
    limit: pagination.limit,
    offset: (pagination.activePage - 1) * pagination.limit,
    search: searchParam.search,
  };

  const { data, isFetching, isError } = useProjectList(projectListParams);
  const results = data?.results || [];

  useEffect(() => {
    if (data?.next || data?.count !== undefined) {
      const newPagination = extractPagination(data.next, data.count);
      setPagination(newPagination);
    }
  }, [data]);

  const handleAddProject = () => {
    navigate(generatePath(AppPaths.addProject));
  };

  const handleProjectDetail = (id: string) => {
    navigate(generatePath(AppPaths.projectDetail, { id }));
  };

  const getVariantByStatus = (variantMap: VariantMapType, status: string): string | undefined => {
    return variantMap[status.toUpperCase()]?.variant;
  };

  const handlePagination = (setPage: number) => {
    setPagination((prev) => ({ ...prev, activePage: setPage }));
  };

  const leadIconProject = (src: string, onClick: () => void) => (
    <Image
      style={{
        cursor: "pointer",
      }}
      onClick={onClick}
      css={styles.leadImage}
      src={src}
      alt="Organization Logo"
    />
  );

  const createColumnData = () =>
    [...[], ...results].map((projectData: any, index: number) => ({
      id: `row-${index}`,
      column1: {
        component: (
          <div css={{ display: "flex", gap: "1rem", alignItems: "center", width: "fit-content" }}>
            {leadIconProject(
              projectData?.assets_grouped_by_category?.logo?.assets?.at(-1)?.url ||
                "https://************.nip.io/roshn_group_logo_with_text_4b8be975b2/roshn_group_logo_with_text_4b8be975b2.svg",
              () => handleProjectDetail(projectData?.id),
            )}
            <RDSTypography
              css={{ cursor: "pointer" }}
              isBold
              fontName={theme?.rds?.typographies?.label?.emphasis?.lg}
              onClick={() => {
                handleProjectDetail(projectData?.id);
              }}
            >
              {projectData?.title}
            </RDSTypography>
          </div>
        ),
      },
      column2: {
        component: (
          <RDSTypography
            css={{ color: theme?.rds?.color?.text?.ui?.tertiary }}
            fontName={theme?.rds?.typographies?.body?.md}
          >{`${projectData?.analytics?.product_sold_count}/${projectData?.analytics?.product_count}`}</RDSTypography>
        ),
      },
      column3: {
        component: (
          <RDSTagBasic
            label={snakeToCapitalizedWords(projectData?.status?.approval_status)}
            size="md"
            appearance={getVariantByStatus(statusVariantMap, projectData?.status?.approval_status)}
          />
        ),
      },
      column4: {
        component: (
          <RDSTagBasic
            label={snakeToCapitalizedWords(projectData?.status?.customer_visibility_status)}
            size="md"
            appearance={getVariantByStatus(
              visibilityVariantMap,
              projectData?.status?.customer_visibility_status,
            )}
          />
        ),
      },
      column5: {
        component: (
          <RDSLinkButton
            onClick={() => handleProjectDetail(projectData?.id)}
            text="View Project"
            size="md"
            variant="default"
            trailIcon={<ChevronRightIcon />}
          />
        ),
      },
    }));

  return (
    <div css={styles.wrapper}>
      <div css={styles.headerContainer}>
        <div css={styles.header()}>
          <div>
            <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>Projects</RDSTypography>
          </div>
          <div>
            <RDSButton onClick={handleAddProject} size="lg" text="+ Add project" />
          </div>
        </div>
        <div>
          <RDSSearchInput
            type="text"
            placeholder="Search by name, city, status..."
            css={styles.searchInput}
            onChange={(e) => {
              setSearch({ searchQuery: e.target.value });
            }}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                setSearchParam({ search: search.searchQuery });
              }
            }}
            onClearSearchInput={() => {
              setSearch({ searchQuery: "" });
              setSearchParam({ search: "" });
            }}
            value={search.searchQuery}
          />
          <div css={styles.tagContainer}>
            {tagData.map((tag) => (
              <RDSTagInteractive
                key={tag.label}
                size="md"
                label={tag.label}
                state={tag.label === activeTag ? "active" : "default"}
                onClick={() => setActiveTag(tag.label)}
              />
            ))}
          </div>
        </div>
      </div>
      {isFetching ? (
        <div css={styles.loaderWrapper}>
          <RoshnContainerLoader />
        </div>
      ) : !isError && results.length > 0 ? (
        <RDSTable
          actionText="View Project"
          // title={tableData.title}
          // description={tableData.description}
          columns={tableData(theme).columns}
          data={createColumnData()}
          slotBottom={
            <RDSPagination
              pageCount={pagination.pageCount}
              activePage={pagination.activePage}
              onPageChange={handlePagination}
            />
          }
        />
      ) : (
        <div css={styles.emptyStateWrapper}>
          <RDSEmptyState
            showMedia={false}
            heading="You don’t have any listed projects"
            size="sm"
            body="Projects you create will appear here. Add new projects now to fill up your list."
            buttons={[
              {
                text: "ADD YOUR FIRST PROJECT",
                variant: "primary",
                onClick: handleAddProject,
              },
            ]}
          />
        </div>
      )}
    </div>
  );
}
