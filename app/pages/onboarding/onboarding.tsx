import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import {
  AppTheme,
  createBoxShadowString,
  RDSButton,
  RDSEmptyState,
  RDSModal,
  RDSProgressSteps,
  RDSTypography,
} from "@roshn/ui-kit";
import { Fragment, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import RoshnLogo from "~/assets/logos/roshn-white-logo.svg?url";
import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { TextArea } from "~/components/form-components/text-area/text-area";
import { useAppPath } from "~/hooks/use-app-path";
import { useStepValidator } from "~/hooks/use-step-validator";
import { AppPaths } from "~/utils/app-paths";

type StepState = "active" | "complete" | "in-complete";

type StepId = "personal-info" | "company-info" | "complete";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const styles = {
  wrapper: {
    display: "flex",
    width: "100vw",
    height: "100vh",
  },

  logo: (theme: AppTheme) =>
    css({
      position: "absolute",
      top: theme.rds.dimension["600"],
      left: theme.rds.dimension["1000"],
      width: "250px",
      height: "80px",
    }),

  contentWrapper: (theme: AppTheme) =>
    css({
      "::-webkit-scrollbar": {
        display: "none",
      },
      scrollbarWidth: "none",
      msOverflowStyle: "none",
      marginBlockStart: "150px",
      width: "100%",
      height: "calc(100% - 150px)",
      backgroundColor: theme.rds.color.background.ui.canvas,
      paddingInline: theme.rds.dimension["2000"],
      paddingBlock: theme.rds.dimension["500"],
      borderRadius: "24px 24px 0 0",
      zIndex: 0,
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      textAlign: "center",
      alignItems: "center",
      paddingBottom: "150px",
      overflowY: "scroll",
    }),

  heading: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h3,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  bottomBar: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.xl,
      width: "100%",
      position: "absolute",
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: theme.rds.color.background.ui.primary.default,
      boxShadow: createBoxShadowString(theme.rds.elevation.onTop["100"]),
      display: "flex",
      justifyContent: "space-between",
      paddingInline: "80px",
      paddingBlock: "24px",
      alignItems: "center",
      color: theme.rds.color.text.ui.primary,
      textTransform: "none",
    }),

  bottomBarButton: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
    }),

  button: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["200"],
    }),

  fieldsWrapper: css({
    width: "100%",
    display: "grid",
    gridTemplateColumns: "repeat(2, 1fr)",
    gap: "24px",
  }),

  emptyStateWrapper: css({
    height: "fit-content",
    "& p": {
      margin: 0,
    },
  }),

  emptyStateBottomBar: css({
    width: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
  }),

  emptyStateButton: css({
    marginBlockStart: "8px",
    textTransform: "none",
  }),
};

const saudiPhoneNumber = z
  .string()
  .regex(/^\+9665\d{8}$/, { message: "Enter a valid phone number" });

export const schema = z.object({
  full_name: z.string().min(1, { message: "First name is required" }).trim(),
  job_title: z.string().min(1, { message: "Job title is required" }).trim(),
  work_email: z.string().email({ message: "Invalid email address" }).trim().toLowerCase(),
  phone_number: saudiPhoneNumber,
  business_type: z.string().min(1, { message: "Business type is required" }).trim(),
  company_name: z.string().min(1, { message: "Company name is required" }).trim(),
  company_registration_number: z.string().min(1, { message: "CRN is required" }).trim(),
  portfolio_website_url: z.string().url({ message: "Invalid URL" }).optional().or(z.literal("")),
  comments: z.string().optional().or(z.literal("")),
});

export default function Onboard() {
  const [showCancelModal, setShowCancelModal] = useState(false);
  const navigate = useNavigate();
  const generateAppPath = useAppPath();

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Personal info", state: "active", id: "personal-info" },
    { stepTitle: "Company info", state: "in-complete", id: "company-info" },
    { stepTitle: "Complete!", state: "in-complete", id: "complete" },
  ]);

  const [currentStep, setCurrentStep] = useState<StepId>("personal-info");

  const handleNav = () => {
    navigate(generateAppPath(AppPaths.activateAccount));
  };

  let timer: unknown;

  useEffect(() => {
    if (currentStep === "complete") {
      timer = setTimeout(() => {
        handleNav();
      }, 5000);
    }

    return () => clearTimeout(timer);
  }, [currentStep]);

  const { control } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      business_type: "",
      comments: "",
      company_name: "",
      company_registration_number: "",
      work_email: "",
      full_name: "",
      job_title: "",
      phone_number: "",
      portfolio_website_url: "",
    },
  });

  const handleNextStep = () => {
    const currentIndex = steps.findIndex((step) => step.id === currentStep);
    if (currentIndex < steps.length - 1) {
      const nextStepId = steps[currentIndex + 1].id;
      handleStepChange(nextStepId);
    }
  };

  const handleStepChange = (stepId: StepId) => {
    setCurrentStep(stepId);
    setSteps((prevSteps) =>
      prevSteps.map((step) => {
        if (step.id === stepId) {
          return { ...step, state: "active" };
        } else if (step.id === "complete") {
          return { ...step, state: "in-complete" };
        }
        return { ...step, state: "complete" };
      }),
    );
  };

  const personalInfo = (
    <Fragment key="personalInfo">
      <RDSTypography css={styles.heading}>Hello! Let’s get you started with ROSHN</RDSTypography>
      <div css={styles.fieldsWrapper}>
        <Input
          data-testid="full-name"
          control={control}
          name="full_name"
          label="Full Name"
          isRequired
          placeholder="Faisal Al-Otaibi..."
          helperText="Provide your full legal name."
        />
        <Input
          data-testid="job-title"
          control={control}
          name="job_title"
          label="Job Title"
          isRequired
          placeholder="Business Development Manager..."
          helperText="Your role or position at the company."
        />
        <Input
          data-testid="work-email"
          control={control}
          name="work_email"
          label="Work email"
          isRequired
          placeholder="<EMAIL>..."
          helperText="Use your company email address to speed up verification."
        />
        <Input
          data-testid="phone-number"
          control={control}
          name="phone_number"
          label="Phone Number"
          isRequired
          placeholder="+966551234567..."
          helperText="Include your country code."
        />
      </div>
    </Fragment>
  );

  const companyInfo = (
    <Fragment key="companyInfo">
      <RDSTypography css={styles.heading}>Provide your company’s information</RDSTypography>
      <div css={styles.fieldsWrapper}>
        <Select
          control={control}
          name="business_type"
          label="Business type"
          data-testid="business-type"
          isRequired
          placeholder="Select business type..."
          helperText="Choose the category that best describes your company."
          options={[
            { label: "Real Estate Developer", value: "real-estate-developer" },
            { label: "Property Management Company", value: "property-management" },
            { label: "Real Estate Broker/Agent", value: "broker-agent" },
          ]}
        />
        <Input
          control={control}
          name="company_name"
          label="Company name"
          data-testid="company-name"
          isRequired
          placeholder="Example inc..."
          helperText="Provide your company’s legal name."
        />
        <Input
          control={control}
          name="company_registration_number"
          label="Commercial Registration Number (CRN)"
          data-testid="commercial-reg-number"
          isRequired
          placeholder="1122334455..."
          helperText="Find this on your official business registration documents."
        />
        <Input
          control={control}
          name="portfolio_website_url"
          data-testid="portfolio-url"
          label="Portfolio or website URL (optional)"
          placeholder="https://example.com..."
          helperText="Provide your company's main portfolio or official website."
        />
      </div>
      <TextArea
        control={control}
        name="comments"
        placeholder="Share any other relevant details here..."
        label="Comments (optional)"
        helperText="Add comments or additional context to support your enrollment."
      />
    </Fragment>
  );

  const complete = (
    <div css={styles.emptyStateWrapper} key="complete">
      <RDSEmptyState
        appearance="success"
        size="sm"
        body="Our team will review your information within 2 business days.
        We’ll notify you by email once your account is activated."
        title="Enrollment submitted successfully!"
        buttons={[
          {
            text: "Learn more about ROSHN Sellers",
            variant: "primary",
            css: { textTransform: "none" },
          },
        ]}
      />
      <div css={styles.emptyStateBottomBar}>
        <RDSTypography>Need help? Our support team is here for you anytime.</RDSTypography>
        <RDSButton
          css={styles.emptyStateButton}
          variant="tertiary"
          size="lg"
          text="Contact Support"
        />
      </div>
    </div>
  );

  const stepRenderer: Record<StepId, JSX.Element> = {
    "personal-info": personalInfo,
    "company-info": companyInfo,
    complete,
  };

  const stepFieldsMap = {
    "personal-info": ["full_name", "job_title", "work_email", "phone_number"],
    "company-info": [
      "business_type",
      "company_name",
      "company_registration_number",
      "portfolio_website_url",
      "comments",
    ],
    complete: [],
  };

  const isValid = useStepValidator(currentStep, control, schema, stepFieldsMap);

  return (
    <>
      <RDSModal
        headerProps={{
          label: "Exit enrollment?",
          type: "centred",
        }}
        isOpen={showCancelModal}
        buttonsGroup={{
          buttons: [
            <RDSButton variant="primary" text="Discard enrollment" key="enrollment" />,
            <RDSButton
              variant="secondary"
              onClick={() => setShowCancelModal(false)}
              text="No, continue working"
              key="continue"
            />,
          ],
          direction: "vertical",
        }}
        description={`You haven’t completed the enrollment form yet. Exiting now will discard all the \ninformation you’ve entered. Are you sure you want to leave?`}
        showContent
        showDescription
      />
      <div css={styles.wrapper}>
        <img src={RoshnLogo} alt="Roshn Logo" css={styles.logo} />

        <div css={styles.contentWrapper}>
          <RDSProgressSteps type="number" steps={steps} size="md" />
          {stepRenderer[currentStep]}
        </div>
        {currentStep !== "complete" && (
          <div css={styles.bottomBar}>
            <RDSTypography>Complete your details to activate your seller account</RDSTypography>
            <div css={styles.bottomBarButton}>
              <RDSButton
                css={styles.button}
                variant="secondary"
                onClick={() => setShowCancelModal(true)}
                size="lg"
                text="Cancel"
              />
              <RDSButton
                onClick={handleNextStep}
                css={styles.button}
                variant="primary"
                size="lg"
                disabled={!isValid}
                text="Continue"
              />
            </div>
          </div>
        )}
      </div>
    </>
  );
}
