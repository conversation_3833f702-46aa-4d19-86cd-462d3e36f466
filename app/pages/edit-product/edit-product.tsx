import { useParams } from "@remix-run/react";
import { useMemo } from "react";

import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import ProductForm from "~/features/unit/unit-form";
import WithLoader from "~/hoc/withLoader";
import { useUnitData } from "~/services/product/hooks/use-unit-data";
import { useProjectDetail } from "~/services/project/hooks/use-project";
import { useGetTemplate } from "~/services/project/hooks/use-get-template";

export const EditProductPage = () => {
  const { id, unitId } = useParams();
  const { data } = useGetTemplate({
    id: 1,
  });

  const productFields = useMemo(() => {
    const sections = data?.product_attributes ?? [];
    return sections.flatMap((section: any) => section.fields || []);
  }, [data]);

  useProjectDetail(id);
  const { data: productData, isLoading } = useUnitData({ id: unitId });
  const defaultValuesFlattened = productData?.custom_attributes?.reduce((acc, curr) => {
    acc[curr.label] = curr.value;
    return acc;
  }, {});

  const defaultValues = structuredClone(defaultValuesFlattened ?? {});

  const convertGalleryImageToUrl = (images: any[]) => {
    return images.map(({ image, ...rest }) => ({
      ...rest,
      url: image,
    }));
  };

  const updateDefaultValues = () => {
    if (!defaultValues) return;
    productFields.forEach(({ custom_attribute_field_key: key, attribute_type }) => {
      const value = defaultValues[key];
      if (attribute_type === "COUNTER") {
        defaultValues[key] = typeof value === "string" ? Number(value) : value;
      }
      if (attribute_type === "GALLERY") {
        const images = convertGalleryImageToUrl(productData["images"]);
        defaultValues[key] = images || [];
      }
    });
    return defaultValues;
  };

  const updatedDefaultValues = useMemo(() => {
    return productData && updateDefaultValues();
  }, [defaultValues, productData]);

  return (
    <WithLoader
      loading={isLoading}
      LoaderComponent={RoshnContainerLoader}
      Component={ProductForm}
      componentProps={{ updatedDefaultValues, productData, productFields, schema: data }}
    />
  );
};
