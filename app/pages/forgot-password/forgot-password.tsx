import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { AppTheme, RDSButton, RDSEmptyState, RDSTypography } from "@roshn/ui-kit";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { BrandWrapper } from "~/components/brand-wrapper/brand-wrapper";
import { Input } from "~/components/form-components/input/input";

type Steps = "email" | "check-inbox" | "reset-password";

const styles = {
  wrapper: css({
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    width: "100vw",
    height: "100vh",
  }),

  successWrapper: (theme: AppTheme) =>
    css({
      padding: theme.rds.dimension["400"],
      backgroundColor: theme.rds.color.background.ui.primary.default,
      height: "fit-content",
    }),

  logo: css({
    width: "130px",
    height: "40px",
    marginBlock: "42px",
  }),

  welcomeText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.primary.secondary,
      marginBlockEnd: theme.rds.dimension["200"],
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      width: "100%",
    }),

  forgotPasswordText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.brand.primary.default,
      cursor: "pointer",
    }),
};

const emailSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export const resetPasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[A-Z]/, "Must include an uppercase letter")
      .regex(/[a-z]/, "Must include a lowercase letter")
      .regex(/[0-9]/, "Must include a number"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export default function ForgotPassword() {
  const [currentStep, setCurrentStep] = useState<Steps>("email");

  const {
    control: emailControl,
    formState: { isValid: emailIsValid },
    watch,
  } = useForm({
    resolver: zodResolver(emailSchema),
    mode: "onChange",
    defaultValues: { email: "" },
  });

  const {
    control: passControl,
    formState: { isValid: isPassValid },
  } = useForm({
    resolver: zodResolver(resetPasswordSchema),
    mode: "onChange",
    defaultValues: { confirmPassword: "", password: "" },
  });

  const handleNextStep = () => {
    if (currentStep === "email") {
      setCurrentStep("check-inbox");
    } else if (currentStep === "check-inbox") {
      setCurrentStep("reset-password");
    }
  };

  const emailAddress = watch().email;

  const email = (
    <BrandWrapper>
      <div css={styles.sectionWrapper}>
        <RDSTypography css={styles.welcomeText}>Forgot your password?</RDSTypography>
        <RDSTypography css={styles.descriptionText}>
          Enter your email address and we will send you a link to reset your password.
        </RDSTypography>
      </div>
      <div css={styles.sectionWrapper}>
        <Input
          control={emailControl}
          label="E-mail"
          data-testid="email"
          placeholder="Enter your email"
          name="email"
        />
      </div>
      <div css={styles.sectionWrapper}>
        <RDSButton
          css={{ textTransform: "none" }}
          variant="primary"
          size="lg"
          text="Send reset link"
          data-testid="reset-link"
          disabled={!emailIsValid}
          onClick={handleNextStep}
        />
        <RDSButton css={{ textTransform: "none" }} variant="secondary" size="lg" text="Log in" />
      </div>
    </BrandWrapper>
  );

  const checkInbox = (
    <div css={styles.successWrapper}>
      <RDSEmptyState
        heading="Check your inbox!"
        appearance="success"
        size="sm"
        body={`We’ve sent a password reset link to ${emailAddress}.
      If you don’t see it soon, please check your spam folder.
      The email will <NAME_EMAIL>.`}
        buttons={[
          {
            text: "Back to Login",
            variant: "primary",
            css: { textTransform: "none" },
            onClick: () => setCurrentStep("reset-password"),
          },
        ]}
      />
    </div>
  );

  const resetPassword = (
    <BrandWrapper>
      <div css={styles.sectionWrapper}>
        <RDSTypography css={styles.welcomeText}>Set a new password</RDSTypography>
        <RDSTypography
          css={styles.descriptionText}
        >{`You are setting a new password for ${emailAddress}.`}</RDSTypography>
      </div>
      <div css={styles.sectionWrapper}>
        <Input
          control={passControl}
          name="password"
          label="New Password"
          placeholder="Enter your new password"
          type="password"
          data-testid="password"
          helperText="Must be at least 8 characters and include letters and numbers."
        />
        <Input
          control={passControl}
          name="confirmPassword"
          label="Confirm Password"
          placeholder="Re-enter your new password"
          data-testid="confirm-pass"
          type="password"
          helperText="Must be at least 8 characters with letters and numbers."
        />
      </div>
      <div css={styles.sectionWrapper}>
        <RDSButton
          css={{ textTransform: "none" }}
          variant="primary"
          size="lg"
          text="Set password and log in"
          data-testid="login-btn"
          disabled={!isPassValid}
        />
      </div>
    </BrandWrapper>
  );

  const stepRenderer = {
    email,
    "check-inbox": checkInbox,
    "reset-password": resetPassword,
  };

  return <div css={styles.wrapper}>{stepRenderer[currentStep]}</div>;
}
