import React from "react";
import { RDSSelect, R<PERSON><PERSON>ink<PERSON>utton, RDS<PERSON>utton, RDSTypography, AppTheme } from "@roshn/ui-kit";
import { snakeToCapitalizedWords } from "~/utils/casing-util";

export enum MemberStatus {
  INVITATION_SENT = "INVITATION_SENT",
  ACTIVE = "ACTIVE",
}

export enum MemberRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export interface MemberData {
  name: string;
  email: string;
  role: MemberRole;
  status: MemberStatus;
}

export interface ColumnRendererConfig {
  type: 'select' | 'button' | 'link-button' | 'text' | 'custom';
  props?: Record<string, any>;
  condition?: (memberData: MemberData) => boolean;
  customRenderer?: (memberData: MemberData) => React.ReactNode;
}

export interface RoleBasedColumnConfig {
  [key: string]: {
    column3?: ColumnRendererConfig[];
    column4?: ColumnRendererConfig[];
  };
}

export function getInitials(fullName: string): string {
  if (!fullName) {
    return "";
  }
  return fullName
    .trim()
    .split(/\s+/)
    .map((word) => word[0]?.toUpperCase() || "")
    .join("");
}

export function renderRoleBasedColumn(
  memberData: MemberData,
  columnConfigs: ColumnRendererConfig[],
  theme: AppTheme,
  styles?: any
): React.ReactNode {
  const config = columnConfigs?.find(config => 
    !config.condition || config.condition(memberData)
  );

  if (!config) {
    return null;
  }

  const { type, props = {}, customRenderer } = config;

  switch (type) {
    case 'select':
      return (
        <RDSSelect
          {...props}
          defaultValue={
            memberData?.role
              ? {
                  value: memberData.role,
                  label: snakeToCapitalizedWords(memberData.role),
                }
              : null
          }
        />
      );

    case 'link-button':
      return (
        <div css={props.containerStyle || {}}>
          <RDSLinkButton
            text={props.text}
            size={props.size || 'md'}
            variant={props.variant || 'default'}
            {...props}
          />
        </div>
      );

    case 'button':
      return (
        <div css={props.containerStyle || {}}>
          <RDSButton
            text={props.text}
            size={props.size || 'md'}
            variant={props.variant || 'primary'}
            {...props}
          />
        </div>
      );

    case 'text':
      return (
        <RDSTypography
          css={styles?.fieldColor?.(theme)}
          fontName={theme?.rds?.typographies?.body?.md}
          {...props}
        >
          {props.text || ''}
        </RDSTypography>
      );

    case 'custom':
      return customRenderer ? customRenderer(memberData) : null;

    default:
      return null;
  }
}

export const teamMembersTagData = [
    { label: "All", state: "active" },
    { label: "Staff", state: "default", status: "STAFF" },
    { label: "Admin", state: "default", status: "ADMIN" },
  ];

export const tableColumns = [
    {
      id: "column1",
      header: "Name",
      accessor: "column1",
    },
    {
      id: "column2",
      header: "Email",
      accessor: "column2",
    },
    {
      id: "column3",
      header: "Role",
      accessor: "column3",
    },
    {
      id: "column4",
      header: "Actions",
      accessor: "column4",
    },
  ];

export const roleOptions = [
  { label: "STAFF", value: "STAFF" },
  { label: "ADMIN", value: "ADMIN" },
  { label: "OWNER", value: "OWNER" },
];

export const roleBasedColumnConfig: RoleBasedColumnConfig = {
  [MemberStatus.ACTIVE]: {
    Role: [
      {
        type: 'select',
        props: {
          options: roleOptions,
          isDisabled: false,
        },
        condition: (memberData: MemberData) => memberData.role !== MemberRole.OWNER,
      },
      {
        type: 'select',
        props: {
          options: roleOptions,
          isDisabled: true,
        },
        condition: (memberData: MemberData) => memberData.role === MemberRole.OWNER,
      },
    ],
    Acrions: [
      {
        type: 'text',
        props: { text: '' },
        condition: () => true,
      },
    ],
  },
  [MemberStatus.INVITATION_SENT]: {
    Role: [
      {
        type: 'link-button',
        props: {
          text: 'RESEND INVITE',
          size: 'md',
          variant: 'default',
          containerStyle: { textAlign: 'center' },
        },
        condition: () => true,
      },
    ],
    Actions: [
      {
        type: 'link-button',
        props: {
          text: 'CANCEL INVITE',
          size: 'md',
          variant: 'default',
          containerStyle: { textAlign: 'center' },
        },
        condition: () => true,
      },
    ],
  },
};
