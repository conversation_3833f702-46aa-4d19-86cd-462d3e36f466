import { ChevronDownIcon } from "@radix-ui/react-icons";
import { AppTheme, RDSLinkButton, RDSSelect, RDSTypography } from "@roshn/ui-kit";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";
import { styles } from "../styles";
import { snakeToCapitalizedWords } from "~/utils/casing-util";
import { roleOptions } from "../constants";

export enum MemberStatus {
  INVITATION_SENT = "INVITATION_SENT",
  ACTIVE = "ACTIVE",
}

export enum MemberRole {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  STAFF = "STAFF",
}

export interface MemberData {
  name: string;
  email: string;
  role: MemberRole;
  status: MemberStatus;
}

export const componentMap = {
  typography: (memberData: MemberData) => <RDSTypography>{memberData?.name}</RDSTypography>,
  select: (memberData: MemberData) => (
    <RDSSelect
      options={roleOptions}
      defaultValue={
        memberData?.role
          ? {
              value: memberData.role,
              label: snakeToCapitalizedWords(memberData.role),
            }
          : null
      }
    />
  ),
  linkButton: (memberData: MemberData) => <RDSLinkButton text="RESEND INVITE" />,
};

export const roleBasedColumns = () => {
  return [
    {
      role: "OWNER",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "ADMIN",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
        { label: "Actions", isSortable: false },
      ],
    },
    {
      role: "STAFF",
      access: [
        {
          label: "Name",
          isSortable: true,
        },
        { label: "Email", isSortable: true },
        { label: "Role", isSortable: true },
      ],
    },
  ];
};

export const roleBasedColumnContent = () => {
  return [
    {
      role: "OWNER",
      memberRole: "OWNER",
      content: [
        {
          label: "Name",
          content: "typography",
        },
        {
          label: "Email",
          content: "typography",
        },
        {
          label: "Role",
          content: "select",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "ADMIN",
      content: [
        {
          label: "Name",
          content: "typography",
        },
        {
          label: "Email",
          content: "typography",
        },
        {
          label: "Role",
          content: "select",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "OWNER",
      memberRole: "STAFF",
      content: [
        {
          label: "Name",
          content: "typography",
        },
        {
          label: "Email",
          content: "typography",
        },
        {
          label: "Role",
          content: "select",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
    {
      role: "ADMIN",
      memberRole: "ADMIN",
      content: [
        {
          label: "Name",
          content: "typography",
        },
        {
          label: "Email",
          content: "typography",
        },
        {
          label: "Role",
          content: "select",
          disabled: true,
        },
        {
          label: "Actions",
          content: null,
        },
      ],
    },
    {
      role: "ADMIN",
      memberRole: "STAFF",
      content: [
        {
          label: "Name",
          content: "typography",
        },
        {
          label: "Email",
          content: "typography",
        },
        {
          label: "Role",
          content: "select",
          disabled: false,
        },
        {
          label: "Actions",
          content: "linkButton",
        },
      ],
    },
  ];
};

export const useRoleBasedColumns = (role: string = "OWNER", theme: AppTheme) => {
  const columnNames = roleBasedColumns().find((column) => column.role === role);
  return columnNames?.access.map((column) => ({
    id: column.label,
    header: (
      <div css={styles.headerWrapper}>
        <RDSTypography isBold fontName={theme?.rds?.typographies?.label?.emphasis?.md}>
          {column.label}
        </RDSTypography>
        {column.isSortable && (
          <AssetWrapper size="20px" type="round">
            <ChevronDownIcon color="green" />
          </AssetWrapper>
        )}
      </div>
    ),
    accessor: column.label,
  }));
};

export const UseRoleBasedTable = (results: MemberData[], role: String = "OWNER") => {
  if(!results) {
    return [];
  }
  console.log(results, "results");
  return results.map((memberData: MemberData, index: number) => {
    return {
      id: `row-${index}`,
      ...roleBasedColumnContent()
        .find((config) => config.role === role && config.memberRole === memberData.role)
        .content.reduce((acc, column) => {
          acc[column.label] = {
            component: componentMap[column.content](memberData),
          };
          return acc;
        }, {}),
    };
  });
};
