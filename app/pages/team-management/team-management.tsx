import { useTheme } from "@emotion/react";
import {
  RDS<PERSON><PERSON>po<PERSON>,
  RDSButton,
  RDSSearchInput,
  RDSTagInteractive,
  AppTheme,
  RDSTable,
  RDSAssetWrapper,
} from "@roshn/ui-kit";
import { useEffect, useState } from "react";
import { styles } from "./styles";
import {
  tableColumns,
  teamMembersTagData,
  MemberData,
  getInitials,
  renderRoleBasedColumn,
  roleBasedColumnConfig
} from "./constants";
import { useTeamMemberList } from "~/services/team-members/hooks/use-team-member-list";
import { RoshnContainerLoader } from "~/features/common/loading/roshn-container-loader";
import { createSvg } from "~/components/svgs";
import InviteMemberModal from "./invite-member-modal";
import isEmpty from "lodash/isEmpty";
import { useGetMyInfo } from "~/services/team-members/hooks/use-my-info";
import { useRoleBasedColumns, UseRoleBasedTable } from "./hooks/use-role-based-table";

export default function TeamManagementPage() {
  const theme = useTheme() as AppTheme;
  const TeamManagementIcon = createSvg(() => import("~/assets/icons/invite-member.svg"));
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [results, setResults] = useState<MemberData[]>([]);
  const [activeTag, setActiveTag] = useState("All");

  const [search, setSearch] = useState({ searchQuery: "" });
  const [searchParam, setSearchParam] = useState({ search: "" });
  const { data: myData, isFetching: isMyDataFetching } = useGetMyInfo();

  const { data, isFetching, isError } = useTeamMemberList({});

  useEffect(() => {
    if (data) {
      const userData = Object.values(data)
        .filter((value) => value)
        .map((value) => value as MemberData);
      setResults(userData);
    }
  }, [data]);

  const getTeamMemberTableColumn = () => {
    const memberTableColumn = useRoleBasedColumns(myData?.role, theme);
    console.log(memberTableColumn, "memberTableColumn");
    return memberTableColumn;
  };

  console.log(UseRoleBasedTable(data?.results, myData?.role), "UseRoleBasedTable");

  const createColumnTable = () => {
    if (isEmpty(results)) {
      return [];
    }

    return results.map((memberData: MemberData, index: number) => {
      const statusConfig = roleBasedColumnConfig[memberData.status];

      return {
        id: `row-${index}`,
        Name: {
          component: (
            <div css={styles.nameColumn}>
              <div css={styles.nameAvatar(theme)}>{getInitials(memberData?.name)}</div>
              <RDSTypography>{memberData?.name}</RDSTypography>
            </div>
          ),
        },
        Email: {
          component: (
            <RDSTypography
              css={styles.fieldColor(theme)}
              fontName={theme?.rds?.typographies?.body?.md}
            >
              {memberData?.email}
            </RDSTypography>
          ),
        },
        Role: {
          component: renderRoleBasedColumn(
            memberData,
            statusConfig?.column3 || [],
            theme,
            styles
          ),
        },
        Actions: {
          component: renderRoleBasedColumn(
            memberData,
            statusConfig?.column4 || [],
            theme,
            styles
          ),
        },
      };
    });
  };

  return (
    <>
      <InviteMemberModal showModal={showInviteModal} onClose={() => setShowInviteModal(false)} />
      <div css={styles.wrapper}>
        <div css={styles.headerContainer}>
          <div css={styles.header()}>
            <div>
              <RDSTypography fontName={theme?.rds?.typographies?.display?.d5}>
                Team members
              </RDSTypography>
            </div>
            <div>
              <RDSButton
                onClick={() => setShowInviteModal(true)}
                size="lg"
                text="INVITE MEMBER"
                leadIcon={
                  <RDSAssetWrapper>
                    <TeamManagementIcon />
                  </RDSAssetWrapper>
                }
              />
            </div>
          </div>
          <div>
            <RDSSearchInput
              type="text"
              placeholder="Search by name or email..."
              css={styles.searchInput}
              onChange={(e) => {
                setSearch({ searchQuery: e.target.value });
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  setSearchParam({ search: search.searchQuery });
                }
              }}
              onClearSearchInput={() => {
                setSearch({ searchQuery: "" });
                setSearchParam({ search: "" });
              }}
              value={search.searchQuery}
            />
            <div css={styles.tagContainer}>
              {teamMembersTagData.map((tag) => (
                <RDSTagInteractive
                  key={tag.label}
                  size="md"
                  label={tag.label}
                  state={tag.label === activeTag ? "active" : "default"}
                  // onClick={() => setActiveTag(tag.label)}
                />
              ))}
            </div>
          </div>
        </div>
        {isFetching || isMyDataFetching ? (
          <div css={styles.loaderWrapper}>
            <RoshnContainerLoader />
          </div>
        ) : (
          !isError && <RDSTable columns={getTeamMemberTableColumn()} data={createColumnTable()} />
        )}
      </div>
    </>
  );
}
