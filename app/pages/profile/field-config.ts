export const profileFields = {
  companyInformation: [
    {
      attribute_type: "TEXT" as const,
      name: "store_name_en",
      label: "Company name (English)",
      placeholder: "Example inc...",
      isRequired: true,
      helperText: "This name will be visible on your public profile page.",
    },
    {
      attribute_type: "TEXT" as const,
      name: "store_name_ar",
      label: "اسم الشركة (بالعربية)",
      placeholder: "Example inc...",
      isRequired: true,
      helperText: "سيظهر هذا الاسم في النسخة العربية من ملفك التعريفي العام.",
      order: 0,
      dir: "rtl",
    },
    {
      attribute_type: "TEXT" as const,
      name: "slogan_en",
      label: "Company slogan (English)",
      placeholder: "Building tomorrow's communities...",
      isRequired: false,
      helperText: "This will appear in the hero section of your profile, just above your logo.",
      order: 1,
    },
    {
      attribute_type: "TEXT" as const,
      name: "slogan_ar",
      label: "شعار الشركة (بالعربية)",
      placeholder: "بناء مجتمعات الغد...",
      isRequired: false,
      helperText: "ستظهر هذه الجملة في القسم العلوي من ملف تعريفك العام، مباشرةً فوق الشعار",
      order: 1,
      dir: "rtl",
    },
    {
      attribute_type: "EDITOR" as const,
      name: "description_en",
      label: "Company description (English)",
      placeholder:
        "This description will be displayed on your public profile. Use this space to introduce yourself, highlight your development experience, and showcase your key projects.",
      isRequired: true,
      helperText: "This description will be visible in the english version of your profile page.",
      limit: 1000,
    },
    {
      attribute_type: "EDITOR" as const,
      name: "description_ar",
      label: "وصف الشركة (بالعربية)",
      placeholder:
        "سيظهر هذا الوصف في ملفك التعريفي العام. يمكنك استخدام هذه المساحة لتعريف نفسك، وإبراز خبراتك في التطوير، وعرض أهم مشاريعك.",
      isRequired: true,
      helperText: "سيظهر هذا الوصف في النسخة العربية من ملفك التعريفي العام.",
      dir: "rtl",
      limit: 1000,
    },
  ],

  brandAssets: [
    {
      attribute_type: "UPLOAD_FILE" as const,
      name: "logo",
      label: "Company logo",
      caption: "JPG or PNG less than 5MB",
      isRequired: true,
    },
    {
      attribute_type: "UPLOAD_FILE" as const,
      name: "banner_image",
      label: "Hero banner",
      caption: "JPG or PNG less than 5MB",
      isRequired: true,
    },
    {
      attribute_type: "COLOR_PICKER" as const,
      name: "color",
      width: "50%",
      label: "Primary brand color",
      caption: "This color defines your profile's theme. Use the color picker or enter a HEX code.",
      isRequired: true,
      defaultValue: "#ffffff",
    },
  ],
  mediaGallery: [
    {
      type: "multiple-images",
      attribute_type: "GALLERY" as const,
      name: "media_gallery",
      label: "Media gallery",
      isRequired: true,
      order: 0,
      caption:
        "Select up to 5 images or videos. Supports JPG, PNG, WEBP, MOV or MP4 less than 5MB. ",
    },
    {
      attribute_type: "BOOLEAN",
      name: "media_section_gallery",
      label: "Add title and description to the media gallery section?",
      options: [
        { label: "Yes", value: "true" },
        { label: "No", value: "false" },
      ],
      order: 1,
    },
    {
      attribute_type: "TEXT" as const,
      name: "gallery_title_en",
      label: "Gallery section title (English)",
      placeholder: "Example inc...",
      order: 2,
      meta: {
        visible: {
          field: "media_section_gallery",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "TEXT" as const,
      name: "gallery_title_ar",
      label: "عنوان لمعرض الوسائط (بالعربية)",
      placeholder: "Example inc...",
      order: 2,
      dir: "rtl",
      meta: {
        visible: {
          field: "media_section_gallery",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "EDITOR" as const,
      name: "gallery_description_en",
      label: "Gallery section description (English)",
      placeholder: "اوصف مهمة وغرض شركتك...",
      limit: 1000,
      helperText:
        "This description will be visible in the english version of your media gallery section",
      meta: {
        visible: {
          field: "media_section_gallery",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "EDITOR" as const,
      name: "gallery_description_ar",
      label: "وصف معرض الوسائط (بالعربية)",
      placeholder: "سيظهر هذا الوصف في معرض الوسائط في ملفك التعريفي.",
      helperText: "سيظهر هذا الوصف في النسخة العربية من معرض الوسائط في ملفك التعريفي",
      dir: "rtl",
      limit: 1000,
      meta: {
        visible: {
          field: "media_section_gallery",
          equals: "true",
        },
      },
    },
  ],
  missionAndVision: [
    {
      attribute_type: "BOOLEAN",
      name: "mission_and_vision",
      label: "Add a mission and vision section to public profile?",
      options: [
        { label: "Yes", value: "true" },
        { label: "No", value: "false" },
      ],
    },
    {
      attribute_type: "EDITOR" as const,
      name: "mission_statement_en",
      label: "Mission Statement (English)",
      placeholder: "Describe your company's mission and purpose...",
      helperText: "Your company's mission statement in English.",
      limit: 150,
      meta: {
        visible: {
          field: "mission_and_vision",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "EDITOR" as const,
      name: "mission_statement_ar",
      label: "بيان المهمة (بالعربية)",
      placeholder: "اوصف مهمة وغرض شركتك...",
      isRequired: false,
      helperText: "بيان مهمة شركتك باللغة العربية.",
      dir: "rtl",
      limit: 50,
      meta: {
        visible: {
          field: "mission_and_vision",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "EDITOR" as const,
      name: "vision_statement_en",
      label: "Vision Statement",
      placeholder: "Describe your company's vision and future goals...",
      helperText: "Your company's vision statement in English.",
      limit: 150,
      meta: {
        visible: {
          field: "mission_and_vision",
          equals: "true",
        },
      },
    },
    {
      attribute_type: "EDITOR" as const,
      name: "vision_statement_ar",
      label: "الرؤية",
      placeholder: "اوصف رؤية وأهداف شركتك المستقبلية...",
      helperText: "لخص تطلعات شركتك المستقبلية",
      limit: 50,
      dir: "rtl",
      meta: {
        visible: {
          field: "mission_and_vision",
          equals: "true",
        },
      },
    },
  ],

  profileContactDetails: [
    {
      attribute_type: "TEXT" as const,
      name: "email",
      label: "Business email address",
      placeholder: "<EMAIL>",
      isRequired: true,
      helperText: "Primary business email address.",
      order: 0,
    },
    {
      attribute_type: "PHONE_NUMBER" as const,
      name: "phone",
      label: "Phone number",
      placeholder: "Enter Phone Number..",
      isRequired: false,
      helperText: "Primary business phone number.",
      order: 0,
    },
    {
      attribute_type: "TEXT" as const,
      name: "website",
      label: "Company website",
      placeholder: "https://example.com",
      isRequired: false,
      helperText: "Your company's official website URL.",
      order: 1,
    },
    {
      attribute_type: "TEXT" as const,
      name: "address",
      label: "Business address",
      placeholder: "Building name, Street, City, Country",
      isRequired: false,
      helperText: "Your main business address.",
      order: 1,
    },
  ],
};
