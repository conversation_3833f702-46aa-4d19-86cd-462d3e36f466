import { css } from "@emotion/react";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDSAs<PERSON>Wrapper, R<PERSON><PERSON><PERSON><PERSON>, RDSTypography } from "@roshn/ui-kit";
import { useEffect, useState } from "react";

import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import ProfileForm from "~/features/profile/profile-form";
import { useAppPath } from "~/hooks/use-app-path";
import { useDynamicForm } from "~/hooks/use-dynamic-form";
import SectionLayout from "~/layouts/SectionLayout";
import {
  useMerchantData,
  useUpdateMerchantMedia,
  useMerchantDataCreate,
  useMerchantDataFileUpload,
  useMerchantDataFileRemoval,
  mapProfileFormToMerchantData,
  mapMerchantDataToFormData,
  mapProfileFormToFileUpload,
  mapProfileFormToFileRemoval,
  hasFilesToUpload,
  type ProfileFormData,
  type MerchantDataResponse,
} from "~/services/merchant-data";
import {
  useBannerFileUpload,
  useMerchantFileUpload,
} from "~/services/merchant-data/hooks/use-merchant-data-file-upload";
import { useMerchantMedia } from "~/services/merchant-data/hooks/use-merchant-media";
import {
  mapProfileBannerFormToFileUpload,
  mapProfileGalleryFormToFileUpload,
} from "~/services/merchant-data/merchant-data-mapper";
import { AppPaths } from "~/utils/app-paths";

import { profileFields } from "./field-config";

export const Eye = createSvg(
  () => import("~/assets/icons/eye.svg") as unknown as Promise<{ ReactComponent: any }>,
);

// Styles
const styles = {
  itemsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),
};

// Main Component
export default function Profile() {
  // State
  const [isUpdating, setIsUpdating] = useState(false);
  const [initialFormData, setInitialFormData] = useState<ProfileFormData | null>(null);
  const [hasExistingData, setHasExistingData] = useState(false);

  const generateAppPath = useAppPath();
  const navigate = useNavigate();
  // Form setup
  const {
    control,
    formState: { isValid, isDirty },
    reset,
    getValues,
    watch,
  } = useDynamicForm(
    [
      ...profileFields.brandAssets,
      ...profileFields.companyInformation,
      ...profileFields.profileContactDetails,
      ...profileFields.mediaGallery,
      ...profileFields.missionAndVision,
    ],
    initialFormData || {},
  );

  // Data fetching
  const {
    data: merchantData,
    isLoading: isLoadingMerchantData,
    error: merchantDataError,
    isSuccess: merchantDataSuccess,
  } = useMerchantData();

  const { data: merchantGallery, isSuccess: merchantGallerySuccess } = useMerchantMedia({
    objectType: "merchant_profile_gallery",
  });

  const { data: merchantBanner, isSuccess: merchantBannerSuccess } = useMerchantMedia({
    objectType: "merchant_profile_banner",
  });

  // Mutations setup
  const createMutation = useMerchantDataCreate();
  const updateMutation = useUpdateMerchantMedia();
  const fileUploadMutation = useMerchantDataFileUpload();
  const fileRemovalMutation = useMerchantDataFileRemoval();
  const sectionUploadMutation = useMerchantFileUpload();
  const bannerUploadMutation = useBannerFileUpload();

  // Helper function to finalize form state with response data
  const finalizeFormState = (responseData: MerchantDataResponse) => {
    try {
      const formData = mapMerchantDataToFormData(responseData);
      setInitialFormData(formData);
      reset(formData, { keepIsValid: true });
      setIsUpdating(false);
    } catch (error) {
      console.error("Error finalizing form state:", error);
      setIsUpdating(false);
    }
  };

  const mapperValidity =
    merchantDataSuccess &&
    merchantData &&
    merchantBannerSuccess &&
    merchantBanner &&
    merchantGallerySuccess &&
    merchantGallery;

  // Process initial merchant data
  useEffect(() => {
    if (mapperValidity) {
      try {
        const formData = mapMerchantDataToFormData({
          ...merchantData,
          merchantBanner,
          merchantGallery,
        });

        setInitialFormData(formData);
        setHasExistingData(true);
        reset(formData, { keepIsValid: true });
      } catch (error) {
        console.error("Error mapping merchant data to form:", error);
        setHasExistingData(false);
        setInitialFormData({} as ProfileFormData);
      }
    } else if (merchantDataError) {
      setHasExistingData(false);
      setInitialFormData({} as ProfileFormData);
    }
  }, [mapperValidity, merchantDataError, reset]);

  // Main submission handler
  const handleSubmit = async () => {
    if (!isValid) return;

    setIsUpdating(true);
    const formData = getValues() as ProfileFormData;

    try {
      // Step 1: Submit data (create or update)
      const dataPayload = mapProfileFormToMerchantData(formData);

      let dataResponse: MerchantDataResponse;
      if (hasExistingData) {
        dataResponse = await updateMutation.mutateAsync(dataPayload);
      } else {
        dataResponse = await createMutation.mutateAsync(dataPayload);
        setHasExistingData(true);
      }

      // Step 2: Handle file operations if needed
      const fileRemovalPayload = initialFormData
        ? mapProfileFormToFileRemoval(formData, initialFormData)
        : null;

      const finalResponse = dataResponse;

      if (hasFilesToUpload(formData)) {
        // Upload new files
        const fileLogoPayload = mapProfileFormToFileUpload(formData);
        const fileBannerPayload = mapProfileBannerFormToFileUpload(formData);
        const fileMediaPayload = await mapProfileGalleryFormToFileUpload(formData);

        if (fileLogoPayload) {
          await fileUploadMutation.mutateAsync(fileLogoPayload);
        }

        if (fileMediaPayload) {
          await sectionUploadMutation.mutateAsync(fileMediaPayload);
        }

        if (fileBannerPayload) {
          await bannerUploadMutation.mutateAsync(fileBannerPayload);
        }
      }

      // Finalize form state with the latest response

      finalizeFormState({ ...finalResponse, ...getValues() });
    } catch (error) {
      console.error("Submit failed:", error);
      setIsUpdating(false);
    }
  };

  // Reset form to initial state
  const handleReset = () => {
    if (initialFormData) {
      reset(initialFormData);
    } else {
      reset({});
    }
  };

  // Computed values
  const isLoading =
    createMutation.isPending ||
    updateMutation.isPending ||
    fileUploadMutation.isPending ||
    fileRemovalMutation.isPending ||
    isUpdating;
  const isInitialLoading = isLoadingMerchantData || !initialFormData;
  const isSubmitDisabled = !isValid || (!isDirty && hasExistingData) || isInitialLoading;

  // Render sections
  const renderPrimarySection = () => {
    if (isInitialLoading) {
      return (
        <Section heading="PROFILE THEME">
          <div style={{ padding: "2rem", textAlign: "center" }}>
            <RDSTypography>Loading profile data...</RDSTypography>
          </div>
        </Section>
      );
    }

    return <ProfileForm control={control} getValues={watch} />;
  };

  const renderSecondarySection = () => {
    return (
      <Section heading="Actions">
        <div css={styles.itemsWrapper}>
          <RDSTypography css={styles.actionsHeadingText}>
            {hasExistingData
              ? "Update your profile information below"
              : "Complete all required fields to create your profile"}
          </RDSTypography>
          <RDSButton
            variant="primary"
            size="md"
            text={hasExistingData ? "UPDATE PROFILE" : "CREATE PROFILE"}
            disabled={isSubmitDisabled}
            loading={isLoading}
            onClick={handleSubmit}
          />
          <RDSButton
            variant="secondary"
            size="md"
            text="DISCARD CHANGES"
            disabled={isInitialLoading}
            onClick={handleReset}
          />
          <RDSButton
            variant="tertiary"
            leadIcon={
              <RDSAssetWrapper>
                <Eye />
              </RDSAssetWrapper>
            }
            size="md"
            text="PREVIEW PUBLIC PROFILE"
            disabled={!hasExistingData}
            href={merchantData?.store_preview_url}
            target="_blank"
          />
        </div>
      </Section>
    );
  };

  return (
    <SectionLayout
      primarySection={renderPrimarySection}
      secondarySection={renderSecondarySection}
      navigation={{
        handleNavigation: () => navigate(generateAppPath(AppPaths.profile)),
        typography: "Back to public profile",
      }}
    />
  );
}
