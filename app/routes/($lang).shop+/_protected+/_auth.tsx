import { Outlet } from "@remix-run/react";

import { authenticationHoc } from "~/hoc/authentication";
import { useGlobalAuthHandler } from "~/hooks/use-global-auth-handler";
import { useMerchantData } from "~/services/merchant-data/hooks/use-merchant-data";
import { AppPaths } from "~/utils/app-paths";

function ProtectedLayout() {
  useGlobalAuthHandler();
  useMerchantData();
  return <Outlet />;
}

export default authenticationHoc(ProtectedLayout, {
  redirectTo: AppPaths.login,
});
