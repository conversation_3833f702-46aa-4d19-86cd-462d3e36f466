import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import z from "zod";

import {
  UploadImageField,
  UploadImageGalleryField,
} from "~/components/form-components/file-upload/image-file-upload";
import { buildFileSchemaValidator } from "~/utils/validator/file-validation";

export default function Anime() {
  const fileSchema = buildFileSchemaValidator({
    maxFiles: 1,
    maxSizeInMB: 5,
    minFiles: 1,
  });

  const attachmentsSchema = buildFileSchemaValidator({
    maxFiles: 10,
    maxSizeInMB: 5,
    minFiles: 5,
  });

  const { control, handleSubmit, getValues } = useForm<any>({
    resolver: zodResolver(
      z.object({
        files: fileSchema,
        attachments: attachmentsSchema,
      }),
    ),
    defaultValues: {
      files: [
        {
          url: "https://gratisography.com/wp-content/uploads/2025/05/gratisography-dino-party-1036x780.jpg",
        },
      ],
      attachments: [
        {
          url: "https://gratisography.com/wp-content/uploads/2025/05/gratisography-dino-party-1036x780.jpg",
        },
      ],
    },
    mode: "onChange",
  });

  console.log("getValues", getValues());

  const onSubmit = (data: any) => {
    console.log("Form submitted with data:", {
      ...data,
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <UploadImageField
        name="files"
        label="Main Documents"
        browseFilePlaceholder="Upload main documents"
        required
        maxSize={0.1 * 1024 * 1024}
        control={control}
      />

      <UploadImageGalleryField
        name="attachments"
        label="Attachments"
        browseFilePlaceholder="Upload attachments"
        required
        maxSize={5 * 1024 * 1024}
        maxFiles={10}
        control={control}
        skipFileManagerValidation={false}
      />

      <button type="submit">Click</button>
    </form>
  );
}
