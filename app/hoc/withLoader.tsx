import React from "react";

interface WithLoaderProps<P extends Record<string, unknown> = Record<string, never>> {
  loading: boolean;
  Component: React.ComponentType<P>;
  componentProps: P;
  LoaderComponent?: React.ComponentType;
}

function WithLoader<P extends Record<string, unknown> = Record<string, never>>({
  loading,
  Component,
  componentProps,
  LoaderComponent,
}: WithLoaderProps<P>) {
  return loading ? (
    <div css={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100vh" }}>
      {LoaderComponent ? <LoaderComponent /> : <div>Loading...</div>}
    </div>
  ) : (
    <Component {...(componentProps as any)} />
  );
}

export default WithLoader;
