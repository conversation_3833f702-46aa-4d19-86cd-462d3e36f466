import { http, HttpResponse } from "msw";

export const exmapleHandlers = [
  http.get("https://api.example.com/user", () => {
    return HttpResponse.json({
      id: "abc-123",
      firstName: "<PERSON>",
      lastName: "Maverick",
    });
  }),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/ecommerce/products/?limit=20&offset=0&search=&search=&ordering=-marketplace_product__date_submitted&marketplace_product_status=",
    () => {
      return HttpResponse.json({
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            buffer_time_after: "00:10:00",
            buffer_time_before: "00:05:00",
            categories: ["Electronics", "Gadgets"],
            combinations: [],
            created_date: "2025-06-30T12:00:00Z",
            cross_sell_groups: [],
            custom_attributes: [],
            description: "A high-end smart speaker with voice assistant support.",
            duration_range: { min: 15, max: 60 },
            durations: [],
            id: 1,
            images: [
              "https://via.placeholder.com/300x300.png?text=Smart+Speaker",
              "https://via.placeholder.com/300x300.png?text=Speaker+Side+View",
            ],
            interval: "daily",
            is_bookable: true,
            is_large_size: false,
            is_popular: true,
            manage_stock: true,
            marketplace_categories: [
              {
                id: 101,
                title: "Smart Devices",
              },
            ],
            marketplace_product: {
              status: "active",
              status_display: "Available",
              code: 200,
              note: "Live on marketplace",
            },
            max_future_booking: 30,
            minimum_notice: 2,
            minimum_notice_unit: "hours",
            minimum_notice_unit_display: "2 Hours",
            modifier_groups: [],
            operating_hours: [],
            per_item_quantity: 1,
            per_item_type: {
              value: "unit",
              label: "Unit",
            },
            price: "149.99",
            price_range: {
              min: "129.99",
              max: "169.99",
            },
            sale_price: "139.99",
            sale_price_range: {
              min: "119.99",
              max: "149.99",
            },
            share_link: "https://shop.example.com/product/smart-speaker",
            sku: "SMART-SPK-001",
            slug: "smart-speaker",
            status: "active",
            stock_quantity: 45,
            title: "Smart Speaker Pro",
            updated_date: "2025-06-30T14:00:00Z",
            variants: [],
            weight: 1.5,
            weight_unit: {
              value: "kg",
              label: "Kilogram",
            },
            youtube_link: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
          },
        ],
      });
    },
  ),

  http.get(
    "https://************.nip.io/cms/api/translations?filters[namespace][$eqi]=home-acquisition&locale=ar",
    () => {
      return HttpResponse.json({
        app: {
          name: "Roshn Seller Dashboard",
          description: "Manage your seller account and products",
          title: "A home designed for you",
        },
        navigation: {
          home: "Home",
          products: "Products",
          orders: "Orders",
          settings: "Settings",
          profile: "Profile",
          logout: "Logout",
        },
        actions: {
          save: "Save",
          cancel: "Cancel",
          delete: "Delete",
          edit: "Edit",
          create: "Create",
          search: "Search",
          filter: "Filter",
          clear: "Clear",
          apply: "Apply",
        },
        messages: {
          loading: "Loading...",
          error: "An error occurred",
          success: "Operation successful",
          confirmDelete: "Are you sure you want to delete this item?",
        },
      });
    },
  ),

  http.get("https://dev-api.shop.myroshn.com/shopboxo/api/v1/merchant/data", () => {
    return HttpResponse.json({
      id: 72,
      store_name: "Retal",
      slug: "real-estate-retal",
      slug_update_counter: 0,
      address: "Saudi Arabia, Ryadh",
      contact_email: "<EMAIL>",
      is_email_verified: true,
      phone: "8003030888",
      whatsapp_phone: "",
      country: "SA",
      description:
        "When Retal Urban Development joined the stellar Al Fozan Group of Companies and began to develop residential, commercial and mixed-use properties.\r\nRetal represents the quintessence of craftsmanship by embracing the virtues of urbanism. The last ten years have seen us reimagine properties using a holistic approach underpinning a unified visionary design principle and creating meaningful destinations with urban real estate solutions. Today, our total asset value accounts for SAR 7+ billion, with 7000+ units developed or under construction.",
      icon: "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/icons/RETAL-LOGO-WHITE-new_RRYMtJ3.png",
      store_type: "shopboxo",
      payment_type: "marketplace_pay",
      payment_types: ["marketplace_pay"],
      email: "<EMAIL>",
      sections: [142],
      payments: {},
      is_min_spend_active: false,
      min_spending: "0.00",
      max_spending: "0.00",
      inventory_tracking_enabled: false,
      navbar_icon:
        "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/merchant_data/navbar_icons/RETAL-LOGO-WHITE-new_HvRZQQO.png",
      navbar_icons: {
        "16": null,
        "32": null,
        "180": null,
      },
      navbar_logo:
        "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/navbar_logos/navbarlogo_Do9og3i.png",
      navbar_logo_position: "center",
      navbar_size: "medium",
      navbar_position: "fixed",
      language: "en",
      time_zone: "UTC",
      currency: 27,
      custom_domains: [],
      currency_code: "SAR",
      show_navbar_logo: true,
      google_analytics_code: null,
      google_analytics_code_alt: null,
      google_tag_manager_container_id: null,
      meta_pixel_id: null,
      palette: {
        id: 19,
        name: "Retal Theme",
        brand_color: "#865232",
        top_bar_color: "#c79474",
        top_bar_text_color: "#FFFFFF",
        background_color: "#865232",
        image: null,
        order: 0,
      },
      global_styles: null,
      product_page_settings: null,
      seller_category: 11,
      seller_category_data: {
        id: 11,
        title: "Developer",
        emoji: "developer",
        icon: null,
      },
      share_text: "",
      privacy_policy: "",
      refund_policy: "",
      shipping_policy: "",
      terms_of_service: "",
      customer_support_number: "8003030888",
      customer_support_email: "<EMAIL>",
      shopping_cart_note: null,
      social_links: null,
      store_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
      store_preview_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
      follow_business_hours: false,
      is_store_open: true,
      closed_until: null,
      is_onboarded: false,
      is_test_merchant: false,
      font_styles: null,
      theme: null,
      is_palette_custom: true,
      is_auto_archive_order_enabled: false,
      is_fnb: false,
      is_bookable: false,
      ecom_plan: [],
      auto_catalog_usage: "NOT_USED",
      is_view_only_link_active: true,
      marketplace: {
        slug: "brokerage",
        title: "ROSHN Brokerage",
        product_reviews_enabled: false,
        admin_product_listing_review_required: false,
        languages: [
          {
            code: "en",
            name: "English",
            name_local: "English",
            name_translated: "English",
            bidi: false,
          },
          {
            code: "ar",
            name: "Arabic",
            name_local: "العربيّة",
            name_translated: "Arabic",
            bidi: true,
          },
        ],
      },
    });
  }),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1/marketplaces/brokerage/inventory-schemas/1",
    () => {
      return HttpResponse.json({
        id: 1,
        name_en: "Off-plan Properties",
        name_ar: "وحدات عقارية على الخارطة",
        slug: "off-plan-properties",
        template_id: "off_plan_properties_v1",
        version: 1,
        description_en:
          "List your under-construction units. Share project details and floor plans to attract buyers before completion.",
        description_ar:
          "أضف وحداتك العقارية تحت التطوير. شارك تفاصيل المشروع والمخططات لجذب المشترين قبل اكتمال البناء.",
        is_active: true,
        order: 0,
        marketplace: 5,
        marketplace_merchant: null,
        product_attributes: [
          {
            id: 33,
            name: "unit code",
            slug: "unit-code",
            attribute_type: "TEXT",
            options: [],
            is_required: true,
            order: 0,
            scope: "PRODUCT",
            section: "unit-details",
            locale: "en",
            label: "Unit code / number",
            placeholder: "Enter unit code or number...",
            helper_text: "Unique identifier visible on the project page.",
            custom_attribute_field_key: "unit_code",
            meta: {},
          },
          {
            id: 34,
            name: "property type",
            slug: "property-type",
            attribute_type: "SELECT",
            options: ["apartment", "villa", "townhouse"],
            is_required: true,
            order: 1,
            scope: "PRODUCT",
            section: "unit-details",
            locale: "en",
            label: "Select property type...",
            placeholder: "Select property type...",
            helper_text: "Specify the type of property (e.g., villa, townhouse).",
            custom_attribute_field_key: "property_type",
            meta: {},
          },
          {
            id: 35,
            name: "bedrooms",
            slug: "bedrooms",
            attribute_type: "COUNTER",
            options: [],
            is_required: false,
            order: 2,
            scope: "PRODUCT",
            section: "unit-details",
            locale: "en",
            label: "Bedrooms",
            placeholder: "0",
            helper_text: "Number of bedrooms in the unit.",
            custom_attribute_field_key: "bedrooms",
            meta: {},
          },
          {
            id: 36,
            name: "bathrooms",
            slug: "bathrooms",
            attribute_type: "COUNTER",
            options: [],
            is_required: true,
            order: 3,
            scope: "PRODUCT",
            section: "unit-details",
            locale: "en",
            label: "Bathrooms",
            placeholder: "0",
            helper_text: "Number of bathrooms in the unit.",
            custom_attribute_field_key: "bathrooms",
            meta: {},
          },
          {
            id: 37,
            name: "image-gallery",
            slug: "image-gallery",
            attribute_type: "GALLERY",
            options: [],
            is_required: true,
            order: 4,
            scope: "PRODUCT",
            section: "media",
            locale: "en",
            label: "image-gallery",
            placeholder: "Browse or drop files",
            helper_text: "Select up to 10 images. Supports JPG, PNG or WEBP less than 5MB.",
            custom_attribute_field_key: "image_gallery",
            meta: {},
          },
          {
            id: 38,
            name: "gross floor area sqm",
            slug: "gross-floor-area-sqm",
            attribute_type: "NUMBER",
            options: [],
            is_required: true,
            order: 5,
            scope: "PRODUCT",
            section: "area-details",
            locale: "en",
            label: "Gross floor area (sqm)",
            placeholder: "Enter gross floor area...",
            helper_text: "Total gross floor area in square meters.",
            custom_attribute_field_key: "gross_floor_area_sqm",
            meta: {},
          },
          {
            id: 39,
            name: "plot area sqm",
            slug: "plot-area-sqm",
            attribute_type: "NUMBER",
            options: [],
            is_required: true,
            order: 6,
            scope: "PRODUCT",
            section: "area-details",
            locale: "en",
            label: "Plot area (sqm)",
            placeholder: "Enter plot area...",
            helper_text: "Size of the plot/land in square meters.",
            custom_attribute_field_key: "plot_area_sqm",
            meta: {},
          },
          {
            id: 40,
            name: "built up area sqm",
            slug: "built-up-area-sqm",
            attribute_type: "NUMBER",
            options: [],
            is_required: true,
            order: 7,
            scope: "PRODUCT",
            section: "area-details",
            locale: "en",
            label: "Built-up area (sqm)",
            placeholder: "Enter built-up area...",
            helper_text: "Built-up area in square meters.",
            custom_attribute_field_key: "built_up_area_sqm",
            meta: {},
          },
          {
            id: 41,
            name: "orientation",
            slug: "orientation",
            attribute_type: "SELECT",
            options: ["north", "west", "east", "south"],
            is_required: false,
            order: 8,
            scope: "PRODUCT",
            section: "area-details",
            locale: "en",
            label: "Unit orientation",
            placeholder: "Select unit orientation...",
            helper_text: "Direction the unit faces (e.g., North, South).",
            custom_attribute_field_key: "orientation",
            meta: {},
          },
          {
            id: 42,
            name: "additional rooms",
            slug: "additional-rooms",
            attribute_type: "MULTI_SELECT",
            options: [
              "driver_room",
              "laundry_room",
              "maid_room",
              "majlis",
              "powder_room",
              "storage_area",
            ],
            is_required: true,
            order: 9,
            scope: "PRODUCT",
            section: "additional-details",
            locale: "en",
            label: "Additional rooms",
            placeholder: null,
            helper_text: "Select available rooms found in the unit.",
            custom_attribute_field_key: "additional_rooms",
            meta: {},
          },
          {
            id: 43,
            name: "unit features",
            slug: "unit-features",
            attribute_type: "MULTI_SELECT",
            options: [
              "accessibility",
              "ac_installed",
              "balcony",
              "broadband_internet",
              "cabinets",
              "cctv_security",
              "elevator",
              "furnished",
              "garden",
              "kitchen_installed",
              "parking",
              "roof",
              "swimming_pool",
              "terrace",
            ],
            is_required: true,
            order: 10,
            scope: "PRODUCT",
            section: "additional-details",
            locale: "en",
            label: "Unit features",
            placeholder: null,
            helper_text: "Select available features and amenities for the unit.",
            custom_attribute_field_key: "unit_features",
            meta: {},
          },
          {
            id: 44,
            name: "price",
            slug: "pricing",
            attribute_type: "CURRENCY",
            options: [],
            is_required: true,
            order: 11,
            scope: "PRODUCT",
            section: "price",
            locale: "en",
            label: "Price",
            placeholder: "Enter price...",
            helper_text: "Full unit price.",
            custom_attribute_field_key: "pricing",
            meta: {},
          },
        ],
        category_attributes: [
          {
            id: 21,
            name: "logo",
            slug: "logo",
            attribute_type: "UPLOAD_FILE",
            options: [],
            is_required: true,
            order: 0,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "Project logo",
            placeholder: "Browse or drop a file",
            helper_text: "JPG or PNG less than 5MB",
            custom_attribute_field_key: "logo",
            meta: {},
          },
          {
            id: 19,
            name: "name",
            slug: "name",
            attribute_type: "TEXT",
            options: [],
            is_required: true,
            order: 1,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "Project name (English)",
            placeholder: "Example inc...",
            helper_text: "This name will be visible in the english version of your project page.",
            custom_attribute_field_key: "name_en",
            meta: {
              layout: {
                width: "50%",
              },
            },
          },
          {
            id: 19,
            name: "name",
            slug: "name",
            attribute_type: "TEXT",
            options: [],
            is_required: true,
            order: 1,
            scope: "CATEGORY",
            section: "project-details",
            locale: "ar",
            label: "اسم المشروع (بالعربية)",
            placeholder: "مثال: مشروع الإبتكار العقاري...",
            helper_text: "سيظهر هذا الاسم في النسخة العربية من صفحة مشروعك.",
            custom_attribute_field_key: "name_ar",
            meta: {
              layout: {
                width: "50%",
              },
            },
          },
          {
            id: 20,
            name: "city",
            slug: "city",
            attribute_type: "SELECT",
            options: ["riyadh", "dammam", "jeddah"],
            is_required: true,
            order: 3,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "City",
            placeholder: "Select a city...",
            helper_text: "Specify the city where the project is located.",
            custom_attribute_field_key: "city",
            meta: {
              layout: {
                width: "50%",
              },
            },
          },
          {
            id: 22,
            name: "rega license number",
            slug: "rega-license-number",
            attribute_type: "NUMBER",
            options: [],
            is_required: true,
            order: 4,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "REGA license number",
            placeholder: "123...",
            helper_text: "Your official project registration number.",
            custom_attribute_field_key: "rega_license_number",
            meta: {
              layout: {
                width: "50%",
              },
            },
          },
          {
            id: 23,
            name: "handover date",
            slug: "handover-date",
            attribute_type: "DATE",
            options: [],
            is_required: true,
            order: 5,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "Handover date",
            placeholder: "Select a date...",
            helper_text: "The date when the project will be delivered to the client.",
            custom_attribute_field_key: "handover_date",
            meta: {
              layout: {
                width: "50%",
              },
            },
          },
          {
            id: 24,
            name: "description",
            slug: "description",
            attribute_type: "EDITOR",
            options: [],
            is_required: true,
            order: 6,
            scope: "CATEGORY",
            section: "project-details",
            locale: "en",
            label: "Description",
            placeholder:
              "Tell us more about your project (max 500 characters). Use formatting to highlight features.",
            helper_text:
              "This description will be visible in the english version of your project page.",
            custom_attribute_field_key: "description_en",
            meta: {},
          },
          {
            id: 24,
            name: "description",
            slug: "description",
            attribute_type: "EDITOR",
            options: [],
            is_required: true,
            order: 6,
            scope: "CATEGORY",
            section: "project-details",
            locale: "ar",
            label: "اسم المشروع (بالعربية)",
            placeholder:
              "أخبرنا المزيد عن مشروعك (بحد أقصى ٥٠٠ حرف). استخدم التنسيق لإبراز الميزات",
            helper_text: "هذا الوصف سيكون مرئيًا في النسخة الإنجليزية من صفحة مشروعك.",
            custom_attribute_field_key: "description_ar",
            meta: {},
          },
          {
            id: 25,
            name: "nearby amenities",
            slug: "nearby-amenities",
            attribute_type: "MULTI_SELECT",
            options: [
              "dining_entertainment",
              "health_centre",
              "kindergarten",
              "mosque",
              "public_park",
              "retail_centres",
              "school",
              "sports_ground",
            ],
            is_required: false,
            order: 7,
            scope: "CATEGORY",
            section: "additional-details",
            locale: "en",
            label: "Nearby amenities",
            placeholder: null,
            helper_text: "Select available amenities in the community.",
            custom_attribute_field_key: "nearby_amenities",
            meta: {},
          },
          {
            id: 32,
            name: "project documents",
            slug: "project-documents",
            attribute_type: "UPLOAD",
            options: [],
            is_required: true,
            order: 8,
            scope: "CATEGORY",
            section: "attachments",
            locale: "en",
            label: "Project documents",
            placeholder:
              "Upload files to share important project details — they will be organized by category for easy access.",
            helper_text: "Examples: brochures, masterplans, floor plans, etc.",
            custom_attribute_field_key: "project_documents",
            meta: {},
          },
          {
            id: 26,
            name: "reservation fee refundable",
            slug: "reservation-fee-refundable",
            attribute_type: "BOOLEAN",
            options: [],
            is_required: true,
            order: 9,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "Refundable?",
            placeholder: null,
            helper_text: "",
            custom_attribute_field_key: "reservation_fee_refundable",
            meta: {
              layout: {
                width: "50%",
              },
              section: {
                label: "Reservation fee:",
                parent_section_attribute_type: "COMPOSITE",
              },
            },
          },
          {
            id: 27,
            name: "reservation fee type",
            slug: "reservation-fee-type",
            attribute_type: "BOOLEAN",
            options: ["fixed", "percentage"],
            is_required: true,
            order: 10,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "Fee Type",
            placeholder: null,
            helper_text: "",
            custom_attribute_field_key: "reservation_fee_type",
            meta: {
              layout: {
                width: "50%",
              },
              section: {
                label: "Reservation fee:",
                parent_section_attribute_type: "COMPOSITE",
              },
            },
          },
          {
            id: 28,
            name: "reservation fee ammount",
            slug: "reservation-fee-ammount",
            attribute_type: "CURRENCY",
            options: [],
            is_required: true,
            order: 11,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "Reservation fee amount (SAR)",
            placeholder: "Enter price...",
            helper_text: "Enter the full amount to be paid for the reservation.",
            custom_attribute_field_key: "reservation_fee_ammount",
            meta: {
              layout: {
                width: "50%",
              },
              section: {
                label: "Reservation fee:",
                parent_section_attribute_type: "COMPOSITE",
              },
            },
          },
          {
            id: 29,
            name: "down payment",
            slug: "down-payment",
            attribute_type: "PERCENTAGE",
            options: [],
            is_required: true,
            order: 12,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "Down payment",
            placeholder: "33.33",
            helper_text: "",
            custom_attribute_field_key: "down_payment",
            meta: {
              section: {
                label: "Payment schedule:",
                columns: ["Payment type", "Amount to pay per installment"],
                parent_section_attribute_type: "PAYMENT",
              },
            },
          },
          {
            id: 30,
            name: "during construction",
            slug: "during-construction",
            attribute_type: "PERCENTAGE",
            options: [],
            is_required: true,
            order: 13,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "During construction",
            placeholder: "33.33",
            helper_text: "",
            custom_attribute_field_key: "during_construction",
            meta: {
              section: {
                label: "Payment schedule:",
                columns: ["Payment type", "Amount to pay per installment"],
                parent_section_attribute_type: "PAYMENT",
              },
            },
          },
          {
            id: 31,
            name: "on handover",
            slug: "on-handover",
            attribute_type: "PERCENTAGE",
            options: [],
            is_required: true,
            order: 14,
            scope: "CATEGORY",
            section: "payment-plan",
            locale: "en",
            label: "On handover",
            placeholder: "33.33",
            helper_text: "",
            custom_attribute_field_key: "on_handover",
            meta: {
              section: {
                label: "Payment schedule:",
                columns: ["Payment type", "Amount to pay per installment"],
                parent_section_attribute_type: "PAYMENT",
              },
            },
          },
        ],
        attributes_count: 26,
        product_attributes_count: 12,
        category_attributes_count: 14,
        categories_using_template: [
          {
            id: 57,
            title: "Doramon",
            slug: "my-merchant-category-34",
          },
          {
            id: 58,
            title: "Doremon",
            slug: "my-merchant-category-35",
          },
          {
            id: 1,
            title: "Ewan Sedra retal test",
            slug: "ewan-sedra",
          },
          {
            id: 2,
            title: "Ewan Sedra-retal-x",
            slug: "ewan-sedra-2",
          },
          {
            id: 3,
            title: "Ewan Warefa",
            slug: "ewan-warefa",
          },
        ],
        created_date: "2025-07-18T10:37:52.044673Z",
        updated_date: "2025-08-06T10:33:11.704001Z",
        marketplace_asset_categories: [
          {
            id: 1,
            name: "Brochures",
            slug: "brochures",
            description: "Brochure for the project or community.",
            allowed_file_types: ["pdf", "jpg", "png", "webp"],
            max_file_size: 5,
            order: 0,
            is_active: true,
            assets_count: 16,
          },
          {
            id: 2,
            name: "Floor Plans",
            slug: "floor-plans",
            description: "Floor Plans for the project or community units.",
            allowed_file_types: ["pdf", "jpg", "png", "webp"],
            max_file_size: 5,
            order: 0,
            is_active: true,
            assets_count: 13,
          },
          {
            id: 4,
            name: "Logo",
            slug: "logo",
            description: "Project or community main logo",
            allowed_file_types: ["jpg", "png", "webp"],
            max_file_size: 5,
            order: 0,
            is_active: true,
            assets_count: 18,
          },
          {
            id: 3,
            name: "Masterplan",
            slug: "masterplan",
            description: "Masterplan for the project or community.",
            allowed_file_types: ["pdf", "jpg", "png", "webp"],
            max_file_size: 5,
            order: 0,
            is_active: true,
            assets_count: 11,
          },
        ],
      });
    },
  ),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/merchant/data/?lang_fallback=true",
    () => {
      return HttpResponse.json({
        id: 72,
        store_name: "Retal",
        store_name_en: "Retal",
        store_name_ar: "رتال",
        slug: "real-estate-retal",
        slug_update_counter: 0,
        address: "",
        address_en: "",
        address_ar: "الرياض",
        contact_email: "<EMAIL>",
        is_email_verified: false,
        phone: "+9665466789087",
        whatsapp_phone: "+9665466789087",
        country: "SA",
        description:
          '<p>When <a target="_blank" rel="noopener noreferrer nofollow" href="https://retal.com.sa/en/">Retal</a> Urban Development joined the stellar Al Fozan Group of Companies and began to develop residential, commercial and mixed-use properties. Retal represents the quintessence of craftsmanship by embracing the virtues of urbanism. The last ten years have seen us reimagine properties using a holistic approach underpinning a unified visionary design principle and creating meaningful destinations with urban real estate solutions. Today, our total asset value accounts for SAR 7+ billion, wit</p>',
        description_en:
          '<p>When <a target="_blank" rel="noopener noreferrer nofollow" href="https://retal.com.sa/en/">Retal</a> Urban Development joined the stellar Al Fozan Group of Companies and began to develop residential, commercial and mixed-use properties. Retal represents the quintessence of craftsmanship by embracing the virtues of urbanism. The last ten years have seen us reimagine properties using a holistic approach underpinning a unified visionary design principle and creating meaningful destinations with urban real estate solutions. Today, our total asset value accounts for SAR 7+ billion, wit</p>',
        description_ar:
          "عندما انضمت شركة رتال للتطوير العمراني إلى مجموعة الفوزان المرموقة، بدأت في تطوير مشاريع سكنية وتجارية ومشاريع متعددة الاستخدامات.\r\nتجسد رتال جوهر الحرفية من خلال تبنّي مبادئ العمران الحضري.\r\nفعلى مدار السنوات العشر الماضية، أعدنا تصور العقارات من خلال نهج شمولي يقوم على مبدأ تصميمي موحّد ورؤية متكاملة، مما مكّننا من إنشاء وجهات ذات معنى وحلول عقارية حضرية.\r\nاليوم، تتجاوز القيمة الإجمالية لأصولنا 7 مليارات ريال سعودي، مع أكثر من 7000 وحدة تم تطويرها أو قيد الإنشاء.",
        icon: "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/icons/images.png",
        mission_statement: "<p>test</p>",
        mission_statement_en: "<p>test</p>",
        mission_statement_ar: "<p>test</p>",
        vision_statement: "<p>test</p>",
        vision_statement_en: "<p>test</p>",
        vision_statement_ar: "<p>testt</p>",
        slogan: "win win",
        slogan_en: "win win",
        slogan_ar: "win win",
        website: "https://www.roshn.sa/sitemap.xml",
        store_type: "shopboxo",
        payment_type: "marketplace_pay",
        payment_types: ["marketplace_pay"],
        email: "<EMAIL>",
        sections: [142],
        payments: {},
        is_min_spend_active: false,
        min_spending: "0.00",
        max_spending: "0.00",
        inventory_tracking_enabled: false,
        navbar_icon: null,
        navbar_icons: {
          "16": null,
          "32": null,
          "180": null,
        },
        navbar_logo:
          "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/navbar_logos/images.png",
        navbar_logo_position: "center",
        navbar_size: "medium",
        navbar_position: "fixed",
        language: "en",
        time_zone: "UTC",
        currency: 27,
        custom_domains: [],
        currency_code: "SAR",
        show_navbar_logo: true,
        google_analytics_code: null,
        google_analytics_code_alt: null,
        google_tag_manager_container_id: null,
        meta_pixel_id: null,
        palette: {
          id: 19,
          name: "Retal Theme",
          brand_color: "#865232",
          top_bar_color: "#2979FF",
          top_bar_text_color: "#FFFFFF",
          background_color: "#F8F8F8",
          image: null,
          order: 0,
        },
        global_styles: {
          brand_color: "#865232",
          top_bar_color: "#2979FF",
          top_bar_text_color: "#FFFFFF",
          background_color: "#F8F8F8",
          side_spacing: 8,
          style_type: "elevated",
          corner_type: "round",
          navigation_type: "classic",
          description_type: "new_page",
          collections_title: "menu",
          tab_bar_display: "title_and_icon",
        },
        product_page_settings: null,
        seller_category: 11,
        seller_category_data: {
          id: 11,
          title: "Developer",
          emoji: "U+1F4AC",
          icon: null,
        },
        share_text: "",
        privacy_policy: "",
        refund_policy: "",
        shipping_policy: "",
        terms_of_service: "",
        customer_support_number: "8003030888",
        customer_support_email: "<EMAIL>",
        shopping_cart_note: null,
        social_links: {
          instagram: "",
          tiktok: "",
          facebook: "",
          twitter: "",
          youtube: "",
          linkedin: "",
          viber_phone: "",
        },
        store_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
        store_preview_url: "https://dev-web.shop.myroshn.com/real-estate-retal",
        follow_business_hours: false,
        is_store_open: true,
        closed_until: null,
        is_onboarded: false,
        is_test_merchant: false,
        font_styles: null,
        theme: null,
        is_palette_custom: true,
        is_auto_archive_order_enabled: false,
        is_fnb: false,
        is_bookable: false,
        ecom_plan: [],
        auto_catalog_usage: "NOT_USED",
        is_view_only_link_active: true,
        media_section_description: true,
        marketplace: {
          slug: "brokerage",
          title: "ROSHN Brokerage",
          product_reviews_enabled: false,
          admin_product_listing_review_required: false,
          admin_category_listing_review_required: true,
          languages: [
            {
              code: "en",
              name: "English",
              name_local: "English",
              name_translated: "English",
              bidi: false,
            },
            {
              code: "ar",
              name: "Arabic",
              name_local: "العربيّة",
              name_translated: "Arabic",
              bidi: true,
            },
          ],
        },
      });
    },
  ),

  http.get(
    "https://dev-api.shop.myroshn.com/shopboxo/api/v1.2/merchant/sections/",
    ({ request }) => {
      const url = new URL(request.url);
      const objectType = url.searchParams.get("object_type");

      if (objectType === "merchant_profile_gallery") {
        return HttpResponse.json({
          id: 1,
          section_type: "gallery",
          object_type: "merchant_profile_gallery",
          icon: null,
          settings: {
            autoplay: true,
            interval: 5000,
            loop: true,
          },
          styles: {
            product_view: "grid",
            internal_spacing: 16,
            top_spacing: 24,
            bottom_spacing: 24,
            alignment: "center",
            aspect_ratio: "16:9",
            background: "image",
            background_color: "#ffffff",
            button_color: "#ff6600",
            button_text_color: "#ffffff",
            collection_name_overlay: false,
            columns_desktop: 3,
            columns_mobile: 1,
            corner_type: "rounded",
            desc_color: "#666666",
            desc_font_size: "14px",
            enabled_quick_add_to_cart: true,
            fill_color: "#f5f5f5",
            height: 300,
            height_size: "medium",
            image_mask: "none",
            image_scalling: "cover",
            is_vertical_product_image: false,
            line_color: "#e0e0e0",
            line_type: "solid",
            position: "center",
            rows: "2",
            show_names: true,
            side_spacing: 20,
            style_type: "modern",
            text_color: "#333333",
            title_color: "#000000",
            title_font_size: "18px",
            use_global_styles: false,
          },
          objects: [
            {
              id: 101,
              title: "Storefront Showcase",
              title_en: "Storefront Showcase",
              title_ar: "واجهة المتجر",
              description: "Highlight your best products.",
              description_en: "Highlight your best products.",
              description_ar: "عرض أفضل المنتجات الخاصة بك.",
              order: 1,
              images: [
                {
                  id: 1001,
                  image:
                    "https://storage.googleapis.com/dev-roshn-backend-shopboxo-static-files/media/shopify/icons/QRCode_TUZrpSf.png",
                  image_alt: "Storefront Image 1",
                  order: 1,
                },
                {
                  id: 1002,
                  image: "https://example.com/images/storefront2.jpg",
                  image_alt: "Storefront Image 2",
                  order: 2,
                },
              ],
            },
          ],
          order: 1,
        });
      }
    },
  ),
];
