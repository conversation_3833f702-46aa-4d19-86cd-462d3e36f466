import { useNavigate, useLocation, createSearchParams } from "@remix-run/react";
import { useEffect } from "react";

import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { HttpClientFactory } from "~/services/http-client-factory";
import { StoreService } from "~/services/store-service/store-service-impl";
import { AppPaths } from "~/utils/app-paths";

export function useGlobalAuthHandler() {
  const navigate = useNavigate();
  const location = useLocation();
  const generateAppPath = useAppPath();
  const httpClientFactory = useInjection<HttpClientFactory>(HttpClientFactory);
  const storeService = useInjection<StoreService>(StoreService);
  const setSignedIn = storeService.state.setSignedIn;

  useEffect(() => {
    const handleUnauthenticated = () => {
      setSignedIn(false);

      const search = createSearchParams({
        redirect: location.pathname + location.search,
      }).toString();

      const loginPath = generateAppPath(AppPaths.login);

      navigate(
        {
          pathname: loginPath,
          search,
        },
        {
          replace: true,
        },
      );
    };

    httpClientFactory.on("unauthenticated", handleUnauthenticated);

    return () => {
      httpClientFactory.off("unauthenticated", handleUnauthenticated);
    };
  }, [navigate, location, generateAppPath, httpClientFactory, setSignedIn]);
}

export function useRedirectIfAuthenticated() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const storeService = useInjection<StoreService>(StoreService);
  const isSignedIn = storeService.state.signedIn;

  useEffect(() => {
    if (isSignedIn) {
      navigate(generateAppPath(AppPaths.dashboard), { replace: true });
    }
  }, [isSignedIn, navigate, generateAppPath]);
}
