import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMemo } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z, ZodTypeAny, ZodRawShape } from "zod";

import { FieldType } from "~/components/field-renderer/field-map";
import { buildFileSchemaValidator } from "~/utils/validator/file-validation";

export type FieldConfig<TName extends string = string> = {
  attribute_type: string;
  name: TName;
  label: string;
  isRequired?: boolean;
  defaultValue?: any;
  meta: {
    visible: {
      field: string;
      equals: string;
    };
  };
};

const zodSchemaMap: Record<string, (field: FieldConfig) => ZodTypeAny> = {
  [FieldType.TEXT]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.SELECT]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.TEXTAREA]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.EDITOR]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.NUMBER]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.BOOLEAN]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.DATE]: ({ isRequired, label }) =>
    isRequired ? z.any({ required_error: `${label} is required` }) : z.any().optional(),

  [FieldType.UPLOAD]: ({ isRequired, label }) =>
    isRequired
      ? z.array(z.instanceof(FormData)).min(1, { message: `${label} is required` })
      : z.array(z.instanceof(FormData)).optional(),

  [FieldType.IMAGE]: ({ isRequired, label }) =>
    isRequired
      ? z.instanceof(File).refine((file) => !!file, { message: `${label} is required` })
      : z.any().optional(),

  [FieldType.MULTI_SELECT]: ({ isRequired, label }) =>
    isRequired
      ? z.array(z.string()).min(1, `${label} is required`)
      : z.array(z.string()).optional(),

  [FieldType.UPLOAD_FILE]: () =>
    buildFileSchemaValidator({ maxSizeInMB: 5, maxFiles: 1, minFiles: 1 }),

  [FieldType.GALLERY]: () =>
    buildFileSchemaValidator({ maxSizeInMB: 5, maxFiles: 10, minFiles: 5 }),

  [FieldType.CURRENCY]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.PERCENTAGE]: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  [FieldType.COUNTER]: ({ isRequired, label }) =>
    isRequired ? z.number().min(1, `${label} is required`) : z.number().optional(),
};

export function useDynamicForm<
  TFieldName extends string = string,
  TValues extends Record<TFieldName, any> = Record<TFieldName, any>,
>(fields: FieldConfig<TFieldName>[], values: Record<TFieldName, any>): UseFormReturn<TValues> {
  const { schema, defaultValues } = useMemo(() => {
    const shape: ZodRawShape = {};
    const initialValues: Partial<Record<TFieldName, any>> = values ?? {};

    fields.forEach((field) => {
      const { name, attribute_type, defaultValue } = field;

      const getSchema = zodSchemaMap[attribute_type];
      shape[name] = getSchema ? getSchema(field) : z.string().optional();

      if (values?.[name] !== undefined) {
        initialValues[name] = values[name];
      } else if (defaultValue !== undefined) {
        initialValues[name] = defaultValue;
      } else if ([FieldType.UPLOAD_FILE, FieldType.GALLERY].includes(attribute_type)) {
        initialValues[name] = null;
      }
    });
    return {
      schema: z.object(shape),
      defaultValues: initialValues,
    };
  }, [fields]);

  return useForm<TValues>({
    resolver: zodResolver(schema),
    mode: "onBlur",
    defaultValues,
    shouldUnregister: true,
  });
}
