import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";

export function useDeleteUnit(options?: UseMutationOptions<any, unknown, { unitId: string }>) {
  const productService = useInjection<ProductService>(ProductService);

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ unitId }) => productService.deleteProduct(unitId),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.PRODUCT_LIST] });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}
