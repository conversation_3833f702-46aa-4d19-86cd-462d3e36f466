import { Dispatch, SetStateAction } from "react";
import { useLayer, UseLayerProps } from "react-laag";

export const usePopover = ({
  setOpen,
  open,
  config,
}: {
  config?: UseLayerProps;
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}) => {
  return useLayer({
    auto: true,
    isOpen: open,
    onOutsideClick: () => setOpen(false),
    placement: "bottom-start",
    possiblePlacements: ["bottom-center", "top-center", "bottom-start", "top-start"],
    // snap: true,
    triggerOffset: 10,
    ...config,
  });
};
