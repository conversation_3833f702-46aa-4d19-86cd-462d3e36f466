import { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { formatMoney } from "~/utils/helper";

export function useMoneyFormatter() {
  const { i18n } = useTranslation();
  const isArabic = i18n.language === "ar";

  const formatter = useCallback(
    (
      amount: number | any,
      options: {
        currency?: string;
        locale?: string;
        numberOfDigits?: number;
        usingCode?: boolean;
      } = {},
    ) => {
      return formatMoney(amount, { ...options, usingCode: !isArabic });
    },
    [isArabic],
  );

  return formatter;
}
