import { useEffect, useState } from "react";

import { bytesToSize, getFileTypeFromUrl } from "~/utils/file-upload-utils";

export type FileSource =
  | File
  | {
      url: string;
      name?: string;
      type?: string;
      size?: number;
      id?: string;
      order?: number;
      image?: string;
    }
  | [];

export type FileData = {
  objectUrl: string | null;
  fileName: string;
  fileType: string;
  fileSize: ReturnType<typeof bytesToSize>;
} | null;

const getFileSize = (source: FileSource): number => {
  if (source instanceof File) {
    return source.size;
  }
  if (typeof source === "object" && "size" in source) {
    return source.size ?? 0;
  }
  return 0;
};

const getFileNameFromUrl = (url: string) => {
  return url.split("/").pop() || "Unknown file";
};

/**
 * Hook to handle both File objects and URL strings
 * @param source - File or URL string
 * @returns objectUrl, fileName, fileSize, fileType
 */
export const useFileUrl = ({ source }: { source: FileSource }): FileData => {
  const [objectUrl, setObjectUrl] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string>("");

  useEffect(() => {
    if (source instanceof File) {
      // Handle File object - create object URL
      const url = URL.createObjectURL(source);
      setObjectUrl(url);
      setFileName(source.name);

      return () => {
        URL.revokeObjectURL(url);
      };
    } else if (typeof source === "string") {
      setObjectUrl(source);
      setFileName(getFileNameFromUrl(source));
    } else if (typeof source === "object" && "url" in source) {
      setObjectUrl(source.url);
      setFileName(source.name || getFileNameFromUrl(source.url));
    } else {
      console.error("Invalid source type", source);
      setObjectUrl(null);
    }

    return () => {
      // No cleanup needed for URL strings
    };
  }, [source]);

  return {
    objectUrl,
    fileName,
    fileSize: bytesToSize(getFileSize(source)),
    fileType:
      source instanceof File
        ? source.type
        : Array.isArray(source)
          ? "unknown"
          : getFileTypeFromUrl(source.url),
  };
};

/**
 * Hook to handle multiple file sources
 * @param sources - Array of FileSource
 * @returns array of FileData
 */
export const useMultipleFileUrls = ({ sources }: { sources: FileSource[] }): FileData[] => {
  const [fileData, setFileData] = useState<
    Array<{
      objectUrl: string;
      fileName: string;
      fileType: string;
      fileSize: ReturnType<typeof bytesToSize>;
    } | null>
  >([]);

  useEffect(() => {
    const objectUrls: string[] = [];

    const data = sources.map((source) => {
      if (source instanceof File) {
        const url = URL.createObjectURL(source);
        objectUrls.push(url);
        return {
          objectUrl: url,
          fileName: source.name,
          fileType: source.type,
          fileSize: bytesToSize(getFileSize(source)),
        };
      }
      if (typeof source === "object" && "url" in source) {
        return {
          objectUrl: source.url,
          fileName: source.name || source.id,
          fileType: source.type || "unknown",
          fileSize: bytesToSize(getFileSize(source)),
        };
      }
      if (typeof source === "string") {
        return {
          objectUrl: source,
          fileName: source.split("/").pop() || "Unknown file",
          fileType: getFileTypeFromUrl(source),
          fileSize: null,
        };
      }
      console.error("Invalid source type", source);
      return null;
    });

    setFileData(data);

    return () => {
      // Cleanup object URLs for File objects
      objectUrls.forEach((url) => URL.revokeObjectURL(url));
    };
  }, [sources]);
  return fileData;
};
