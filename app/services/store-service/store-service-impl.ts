import { injectable, inject } from "inversify";

import { StoreFactory, PersistedStore } from "~/services/store-factory";
import {
  createAuthSlice,
  createProfileSlice,
  AuthSlice,
  ProfileSlice,
  MerchantDataSlice,
} from "~/store/slices";
import { createMerchantDataSlice } from "~/store/slices/merchant-data-slice";

export type GlobalStore = PersistedStore<AuthSlice & ProfileSlice & MerchantDataSlice>;

@injectable()
export class StoreService {
  private readonly storeInstance: GlobalStore;

  constructor(
    @inject(StoreFactory)
    private readonly storeFactory: StoreFactory,
  ) {
    this.storeInstance = this.storeFactory.createStore<
      AuthSlice & ProfileSlice & MerchantDataSlice
    >(
      (...a) => ({
        ...createAuthSlice(...a),
        ...createProfileSlice(...a),
        ...createMerchantDataSlice(...a),
      }),
      {
        persist: {
          name: "global-store",
          version: 0,
          partialize: (state) => ({
            ...state,
          }),
        },
      },
    );
  }

  public getStore(): GlobalStore {
    return this.storeInstance;
  }

  public get state(): ReturnType<GlobalStore["getState"]> {
    return this.storeInstance.getState();
  }
}
