import { AxiosResponse, HttpStatusCode, isAxiosError } from "axios";
import delay from "delay";
import { inject, injectable, unmanaged } from "inversify";

import { EnvService } from "~/services/env";

import { type HttpClient, HttpClientFactory, HttpRequestConfig } from "../http-client-factory";

import { isRestSuccess } from "./guards";
import {
  CommonRestErrorCodes,
  RestError,
  RestErrorCode,
  RestHelper,
  RestSuccess,
  isRestOkCode,
  PollingArgs,
  PollingTimeoutError,
} from "./rest-helper";

const TRACEPARENT_HEADER = "traceparent";

const wrapAxiosPromise = async <R, E extends RestErrorCode>(
  promise: Promise<AxiosResponse<RestSuccess<R> | RestError<E>>>,
) => {
  try {
    const response = await promise;
    const traceId = response?.headers?.[TRACEPARENT_HEADER];
    // setTraceIdInSessionStorage(traceId);

    let data;

    if (typeof response?.data === "string") {
      data = {
        __TRACE_ID__: traceId,
        code: CommonRestErrorCodes.UNKNOWN_ERROR,
        data: response?.data,
      };
    } else {
      data = {
        ...(response?.data as RestSuccess<R>),
        __TRACE_ID__: traceId,
      };
    }

    return data;
  } catch (error) {
    const wrappedError = {} as RestError<E> & {
      _innerError?: unknown;
    };
    wrappedError.code = CommonRestErrorCodes.UNKNOWN_ERROR as E;
    wrappedError.message = CommonRestErrorCodes.UNKNOWN_ERROR;

    wrappedError._innerError = error;

    // convert error response to RestError
    if (!isAxiosError<RestError<E>>(error) || !error.response) {
      wrappedError.code = CommonRestErrorCodes.UNKNOWN_ERROR as E;
      wrappedError.message = CommonRestErrorCodes.UNKNOWN_ERROR;

      if (error instanceof Error) {
        wrappedError.message = error.message;
      }

      return wrappedError;
    }

    if (error.response.status >= HttpStatusCode.InternalServerError) {
      wrappedError.code = CommonRestErrorCodes.SERVER_ERROR as any;
      wrappedError.message = CommonRestErrorCodes.SERVER_ERROR;

      return wrappedError;
    }

    if (typeof error.response?.data?.code !== "string") {
      wrappedError.code = CommonRestErrorCodes.UNKNOWN_ERROR as any;
      wrappedError.message = CommonRestErrorCodes.UNKNOWN_ERROR;

      return wrappedError;
    }

    wrappedError.code = error.response?.data?.code;
    wrappedError.message = error.response?.data?.message;

    return wrappedError;
  }
};

@injectable()
export class BaseRestHelperImpl implements RestHelper {
  readonly axios;

  constructor(@unmanaged() private readonly httpClient: HttpClient) {
    this.axios = this.httpClient.axios;
  }

  async unwrap<R, E extends string>(promise: Promise<RestSuccess<R> | RestError<E>>): Promise<R> {
    const result = await promise;
    // if (!isRestOkCode(result.code)) {
    //   throw result;
    // }
    return result as R;
  }

  get<R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<RestSuccess<R> | RestError<E>> {
    return wrapAxiosPromise<R, E>(this.httpClient.get(url, config));
  }

  head<R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<RestSuccess<R> | RestError<E>> {
    return wrapAxiosPromise<R, E>(this.httpClient.head(url, config));
  }

  post<R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>> {
    return wrapAxiosPromise<R, E>(this.httpClient.post(url, config?.data, config));
  }

  put<R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>> {
    return wrapAxiosPromise<R, E>(this.httpClient.put(url, config?.data, config));
  }

  patch = <R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D>,
  ): Promise<RestSuccess<R> | RestError<E>> => {
    return wrapAxiosPromise<R, E>(this.httpClient.patch(url, config?.data, config));
  };

  delete = <R, E extends string, D = any>(
    url: string,
    config?: HttpRequestConfig<D> | undefined,
  ): Promise<RestSuccess<R> | RestError<E>> => {
    return wrapAxiosPromise<R, E>(this.httpClient.delete(url, config));
  };

  poll = async <R, E extends string, D = any>(
    config: PollingArgs<R, D>,
  ): Promise<RestSuccess<R> | RestError<E | PollingTimeoutError>> => {
    const { predicate, pollingInterval = 3000, pollingTimeout = 120000, ...rest } = config;

    const now = Date.now();

    let candidate: RestSuccess<R> | RestError<E> | undefined = undefined;
    while (Date.now() - now < pollingTimeout) {
      candidate = await wrapAxiosPromise<R, E>(
        this.httpClient.request({
          ...rest,
        }),
      );

      if (!isRestSuccess(candidate)) {
        return candidate;
      }

      if (predicate(candidate.data)) {
        return candidate;
      }

      await delay(pollingInterval);
    }

    return {
      code: PollingTimeoutError,
      innerError: candidate,
    };
  };
}

@injectable()
export class RestHelperImpl extends BaseRestHelperImpl {
  constructor(
    @inject(HttpClientFactory) httpClientFactory: HttpClientFactory,
    @inject(EnvService) envService: EnvService,
  ) {
    super(
      httpClientFactory.create({
        baseURL: envService.APP_API_URL,
      }),
    );
  }
}
