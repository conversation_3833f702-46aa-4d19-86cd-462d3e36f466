import { useQuery } from "@tanstack/react-query";

import { Query<PERSON><PERSON> } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { TeamMemberListArgs, TeamMemberService } from "../team-members";

export function useTeamMemberList(args: TeamMemberListArgs) {
  const teamMemberService = useInjection<TeamMemberService>(TeamMemberService);

  return useQuery({
    queryKey: [QueryKey.TEAM_MEMBERS, args],
    queryFn: () => teamMemberService.getTeamMembers(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: true,
    staleTime: 2 * 1000,
  });
}