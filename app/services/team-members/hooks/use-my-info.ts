import { useQuery } from "@tanstack/react-query";

import { QueryK<PERSON> } from "~/context/reactQueryProvider";

import { useInjection } from "~/hooks/use-di";
import { TeamMemberService } from "~/services/team-members/team-members";

export function useGetMyInfo() {
  const teamMemberService = useInjection<TeamMemberService>(TeamMemberService);

  return useQuery({
    queryKey: [QueryKey.TEAM_MEMBERS],
    queryFn: () => teamMemberService.getMyInfo(),
    select: (data) => data,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: true,
    staleTime: 2 * 1000,
  });
}
