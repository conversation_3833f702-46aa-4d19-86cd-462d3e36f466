import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { InviteMemberPayload, TeamMemberService } from "~/services/team-members/team-members";

export function useInviteMember(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      payload: InviteMemberPayload;
    }
  >,
) {
  const teamMemberService = useInjection<TeamMemberService>(TeamMemberService);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ payload }: { payload: InviteMemberPayload }) => teamMemberService.inviteMember(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.TEAM_MEMBERS] });
    },
    ...options,
  });
}
