import { TeamMemberImpl } from "./team-members-impl";

export type TeamMemberListArgs = {
  search?: string;
  limit?: number;
  offset?: number;
  status?: string;
};

export type InviteMemberPayload = {
  email: string;
  role: string;
};

export interface TeamMembersServiceInterface {
  getTeamMembers: (args: TeamMemberListArgs) => Promise<any>;
  inviteMember: (payload: InviteMemberPayload) => Promise<any>;
  getMyInfo: () => Promise<any>;
}

export type TeamMemberService = TeamMemberImpl;
export const TeamMemberService = Symbol("TeamMemberService");
