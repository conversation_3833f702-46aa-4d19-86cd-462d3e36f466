import { inject, injectable } from "inversify";

import { MerchantDataService } from "../merchant-data/merchant-data";
import { RestHelper } from "../rest-helper";
import { TemplateImpl } from "../template/template-impl";

import { Category } from "./category";
import { CreateMerchantCategoryPayload } from "./types";

@injectable()
export class CategoryImpl extends TemplateImpl implements Category {
  constructor(
    @inject(RestHelper)
    protected readonly restHelper: RestHelper,
    @inject(MerchantDataService)
    protected readonly merchantDataService: MerchantDataService,
  ) {
    super(restHelper, merchantDataService);
  }

  getCategoryWithData = async ({ id }: { id: string | number }) => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}/`;
    return this.restHelper.get(url);
  };

  getCategoryById: () => Promise<any>;

  addAsset = async ({ id, payload }: { id: number; payload: any }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}/assets/`;
    return this.restHelper.post(url, {
      headers: {
        "content-type": "multipart/form-data",
      },
      data: payload,
    });
  };

  addCategory = async ({ payload }: { payload: CreateMerchantCategoryPayload }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/`;

    return this.restHelper.post(url, { data: payload });
  };

  updateCategory = async ({
    payload,
    categoryId,
  }: {
    payload: CreateMerchantCategoryPayload;
    categoryId: number;
  }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${categoryId}/`;

    return this.restHelper.patch(url, { data: payload });
  };

  categoryReview = async ({
    payload,
    categoryId,
  }: {
    payload: {
      status: string;
    };
    categoryId: number;
  }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${categoryId}/status/`;

    return this.restHelper.post(url, { data: payload });
  };

  changeVisibility = async ({
    payload,
    categoryId,
  }: {
    payload: {
      visibility_status: string;
    };
    categoryId: number | string;
  }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;

    const url = `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${categoryId}/visibility/`;

    return this.restHelper.post(url, { data: payload });
  };
}
