import { Template } from "../template/template";

export interface Category extends Template {
  getCategoryWithData: ({ id }: { id: string | number }) => Promise<any>;

  addCategory: ({ payload }: { payload: any }) => Promise<any>;

  addAsset: ({ id, payload }: { id: number; payload: any }) => Promise<any>;

  getCategoryById: () => Promise<any>;

  updateCategory: ({ categoryId, payload }: { categoryId: number; payload: any }) => Promise<any>;

  categoryReview: ({
    payload,
    categoryId,
  }: {
    payload: {
      status: string;
    };
    categoryId: number;
  }) => Promise<any>;

  changeVisibility: ({
    payload,
    categoryId,
  }: {
    payload: {
      visibility_status: string;
    };
    categoryId: number | string;
  }) => Promise<any>;
}

export const Category = Symbol("Category");
