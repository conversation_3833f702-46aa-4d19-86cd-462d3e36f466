export interface MarketplaceCategory {
  id: number;
  title: string;
  slug: string;
}

export interface MarketplaceMerchant {
  id: number;
  slug: string;
}

export interface CustomAttributes {
  [key: string]: any;
}

export interface MerchantCategoryStatus {
  approval_status: string;
  approval_status_display: string;
  customer_visibility_status: string;
}

export interface ReviewTimeline {
  id: number;
  category: number;
  action: string;
  note: string;
  prev_value: string | null;
  new_value: string;
  created_date: string;
}

export interface SchemaTemplate {
  id: number;
  name: string;
  template_id: string;
}

export interface ProductCustomAttribute {
  label: string;
  value: string;
}

export interface MarketplaceProduct {
  id: number;
  status: string;
  status_display: string;
  code: number;
  note: string;
  date_submitted: string;
  marketplace_merchant_category: {
    id: number;
    title: string;
    slug: string;
  };
}

export interface Product {
  id: number;
  title: string;
  slug: string;
  description: string;
  status: string;
  status_display: string;
  price: string;
  images: any[];
  marketplace_categories: MarketplaceCategory[];
  custom_attributes: ProductCustomAttribute[];
  marketplace_product: MarketplaceProduct;
  created_date: string;
  updated_date: string;
}

export interface AnalyticsRange {
  min: number | null;
  max: number | null;
}

export interface MerchantAnalytics {
  product_count: number;
  product_sold_count: number;
  property_types: string[];
  price_range: AnalyticsRange;
  plot_area_range: AnalyticsRange;
  bedrooms_range: AnalyticsRange;
  total_active_products: number;
  total_visible_products: number;
}

export interface MerchantCategory {
  id: number;
  merchant: number;
  marketplace_category: MarketplaceCategory;
  marketplace_merchant: MarketplaceMerchant;
  title: string;
  slug: string;
  status: MerchantCategoryStatus;
  custom_attributes: CustomAttributes;
  rega_licence_number: string | null;
  applied_templates: number[];
  parent_category_info: any | null;
  version_number: number;
  is_live_version: boolean;
  order: number;
  merchant_category_assets: any[];
  assets_grouped_by_category: Record<string, any>;
  assets_count: number;
  review_timelines: ReviewTimeline[];
  schema_template: SchemaTemplate[];
  created_date: string;
  updated_date: string;
  products_count: number;
  products: Product[];
  analytics: MerchantAnalytics;
}

export interface PaginatedMerchantCategoryResponse {
  next: string | null;
  previous: string | null;
  results: MerchantCategory[];
}

export interface CreateMerchantCategoryPayload {
  title: string;
  template_id: number;
  custom_attributes: MerchantCustomAttributes;
  order: number;
}

export interface MerchantCustomAttributes {
  logo?: {
    _uploaded: boolean;
  };
  name_en: string;
  name_ar: string;
  city: string;
  rega_license_number: string;
  handover_date: string;
  description: string;
  nearby_amenities: string[];
  project_documents: Record<string, any>[];

  reservation_fee_refundable: "Yes" | "No";
  reservation_fee_type: "Fixed" | "Percentage";
  reservation_fee_ammount: string;

  down_payment: string;
  during_construction: string;
  on_handover: string;
}
