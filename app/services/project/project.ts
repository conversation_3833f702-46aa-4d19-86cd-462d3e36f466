import { ProjectServiceImpl } from "./project-impl";

export type QueryProjectListParams = {
  search: string;
  limit: number;
  offset: number;
  status?: string;
};

export interface ProjectServiceInterface {
  getProjectList: (args: { limit: number; offset: number }) => Promise<any>;
  getProjectDetail: (id: string) => Promise<any>;
  deleteProject: (id: string) => Promise<any>;
  get categoryDetails(): any;
}

export type ProjectService = ProjectServiceImpl;
export const ProjectService = Symbol("ProjectService");
