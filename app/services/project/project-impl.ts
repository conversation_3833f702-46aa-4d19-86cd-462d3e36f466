import { inject, injectable } from "inversify";

import { <PERSON>Helper } from "~/services/rest-helper";

import { MerchantDataService } from "../merchant-data/merchant-data";
import { StoreService } from "../store-service/store-service-impl";

import { ProjectServiceInterface } from "./project";

@injectable()
export class ProjectServiceImpl implements ProjectServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
    @inject(MerchantDataService)
    private readonly merchantDataService: MerchantDataService,
    @inject(StoreService)
    private readonly storeService: StoreService,
  ) {}

  getProjectList = (args: { limit: number; offset: number; search: string }): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    return this.restHelper.get(`/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/`, {
      params: {
        include_details: true,
        ...args,
      },
    });
  };

  getProjectDetail =  async (id: string): Promise<any> =>  {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    const data = await this.restHelper.get(
      `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}/`,
    );
    this.storeService.state.setCategoryDetails(data);
    return data;
  };

  deleteProject = async (id: string): Promise<any> => {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    return this.restHelper.delete(
      `/api/v1/marketplaces/${marketPlaceSlug}/merchants/categories/${id}`,
    );
  };

  get categoryDetails() {
    return this.storeService.state.categoryDetails;
  }
}
