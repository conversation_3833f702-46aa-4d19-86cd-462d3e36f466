import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { ProjectServiceInterface, ProjectService } from "~/services/project/project";

export function useDeleteProject(options?: UseMutationOptions<any, unknown, { id: string }>) {
  const projectService = useInjection<ProjectServiceInterface>(ProjectService);

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }) => projectService.deleteProject(id),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.PRODUCT_LIST] });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}
