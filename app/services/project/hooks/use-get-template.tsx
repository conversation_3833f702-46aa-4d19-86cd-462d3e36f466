import { css } from "@emotion/react";
import { AppTheme, RDSTypography } from "@roshn/ui-kit";
import { useQuery } from "@tanstack/react-query";

import { FieldType } from "~/components/field-renderer/field-map";
import { currencySymbol } from "~/constants/currency-symbol";
import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";
import { toTitle } from "~/utils/casing-util";

export type Field = {
  id: number;
  name: string;
  slug: string;
  attribute_type: FieldType;
  options: { value: string; label: string }[];
  is_required: boolean;
  is_translatable: boolean;
  order: number;
  scope: string;
  section: string;
  custom_attribute_field_key: string;
  custom_attribute_field_keys: string;
  label: string;
  placeholder: string;
  helper_text: string;
  isRequired: boolean;
  locale: "en" | "ar";
};

type RawField = Omit<Field, "helperText" | "isRequired" | "options"> & {
  options: string[];
};

const styles = {
  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
      height: "fit-content",
    }),
};

function enrichField(field: RawField): Field {
  const boolFieldType = field.attribute_type === "BOOLEAN";
  const enrichedOptions = field.options?.length
    ? field.options.map((opt) => ({ value: opt, label: toTitle(opt) }))
    : boolFieldType
      ? [
          { label: "Yes", value: "true" },
          { label: "No", value: "false" },
        ]
      : [];

  const commonProps = {
    name: field.custom_attribute_field_key,
    options: enrichedOptions,
    helperText: field.helper_text,
    isRequired: field.is_required,
    ...(field.locale === "ar" && { dir: "rtl" }),
  };

  const propsConfig = {
    [FieldType.DATE]: {
      name: field.custom_attribute_field_key,
      placeholder: field.placeholder,
      dateInputProps: {
        placeholder: field.placeholder,
        label: field.label,
        helperText: field.helper_text,
        isRequired: field.is_required,
        ...(field.locale === "ar" && { dir: "rtl" }),
      },
    },
    [FieldType.NUMBER]: {
      ...commonProps,
      type: "number",
    },
    [FieldType.UPLOAD_FILE]: {
      ...commonProps,
      caption: field.helper_text,
    },
    DEFAULT: commonProps,

    [FieldType.CURRENCY]: {
      ...commonProps,
      type: "number",
      leadIcon: <RDSTypography css={styles.listingTypo}>{currencySymbol}</RDSTypography>,
    },
    [FieldType.PERCENTAGE]: {
      ...commonProps,
      type: "number",
      suffixText: "%",
    },
    [FieldType.COUNTER]: {
      ...commonProps,
      type: "number",
    },

    [FieldType.GALLERY]: {
      ...commonProps,
      caption: field.helper_text,
    },
  };

  const fieldConfig = propsConfig[field?.attribute_type] || propsConfig["DEFAULT"];

  return {
    ...field,
    ...fieldConfig,
  };
}

function groupAndSortFields(fields: RawField[]) {
  const groupedMap: Record<string, Field[]> = {};

  fields.forEach((raw) => {
    const field = enrichField(raw);
    if (!groupedMap[field.section]) groupedMap[field.section] = [];
    groupedMap[field.section].push(field);
  });

  return Object.entries(groupedMap)
    .map(([section, fields]) => ({
      section,
      fields: fields.sort((a, b) => a.order - b.order),
    }))
    .sort((a, b) => (a.fields[0]?.order ?? 0) - (b.fields[0]?.order ?? 0));
}

export function useGetTemplate({ id }: { id: string | number }) {
  const categoryService = useInjection<Category>(Category);

  return useQuery({
    queryKey: [QueryKey.CATEGORY_TEMPLATE, id],
    queryFn: () => categoryService.getTemplateById({ id }),
    select: (data) => {
      const groupedSections = groupAndSortFields(data.category_attributes || []);

      const productSections = groupAndSortFields(data.product_attributes || []);

      const marketplaceAssetCategories = data.marketplace_asset_categories || [];

      const updatedSections = (mappedSection) =>
        mappedSection.map((section) => ({
          ...section,
          fields: section.fields.map((field) => {
            if (field.attribute_type !== "UPLOAD_FILE" && field.attribute_type !== "UPLOAD")
              return field;

            if (field.attribute_type === "UPLOAD") {
              const validSlugs = ["brochures", "floor-plans", "masterplan"];

              field.attachments = marketplaceAssetCategories.filter((category) =>
                validSlugs.includes(category.slug),
              );
            }

            const matched = marketplaceAssetCategories.find((cat) => {
              return field.slug === cat.slug || field.name.toLowerCase() === cat.name.toLowerCase();
            });

            return matched
              ? {
                  ...field,
                  attribute_slug: matched.slug,
                  asset_id: matched.id,
                  asset_order: matched.order,
                  asset_des: matched.description,
                }
              : field;
          }),
        }));

      return {
        ...data,
        category_attributes: updatedSections(groupedSections),
        product_attributes: updatedSections(productSections),
      };
    },
    staleTime: Infinity,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    structuralSharing: true,
  });
}
