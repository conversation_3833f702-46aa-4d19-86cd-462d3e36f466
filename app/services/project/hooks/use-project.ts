import { useQuery } from "@tanstack/react-query";

import { QueryK<PERSON> } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";

import { ProjectService, QueryProjectListParams } from "../project";


export function useProjectList(args: QueryProjectListParams) {
  const projectService = useInjection<ProjectService>(ProjectService);

  return useQuery({
    queryKey: [QueryKey.PROJECT_LIST, args],
    queryFn: () => projectService.getProjectList(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}

export function useProjectDetail(args: string) {
  const projectService = useInjection<ProjectService>(ProjectService);

  return useQuery({
    queryKey: [QueryKey.PROJECT_DETAIL, args],
    queryFn: async () => projectService.getProjectDetail(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}
