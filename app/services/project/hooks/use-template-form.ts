import { zodResolver } from "@hookform/resolvers/zod";
import { UseQueryResult } from "@tanstack/react-query";
import { useMemo } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { z, ZodTypeAny, ZodRawShape } from "zod";

import { useGetTemplate } from "./use-get-template";

export type FieldConfig<TName extends string = string> = {
  attribute_type: string;
  name: TName;
  label: string;
  isRequired?: boolean;
  defaultValue?: any;
};

const zodSchemaMap: Record<string, (field: FieldConfig) => ZodTypeAny> = {
  TEXT: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  TEXTAREA: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  EDITOR: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  NUMBER: ({ isRequired, label }) =>
    isRequired ? z.string().min(1, `${label} is required`) : z.string().optional(),

  BOOLEAN: ({ isRequired, label }) =>
    isRequired
      ? z.enum(["yes", "no"], {
          errorMap: () => ({ message: `${label} is required` }),
        })
      : z.enum(["yes", "no"]).optional(),

  DATE: ({ isRequired, label }) =>
    isRequired ? z.date({ required_error: `${label} is required` }) : z.date().optional(),

  UPLOAD: ({ isRequired, label }) =>
    isRequired
      ? z.any().refine((file) => !!file, { message: `${label} is required` })
      : z.any().optional(),

  IMAGE: ({ isRequired, label }) =>
    isRequired
      ? z.any().refine((file) => !!file, { message: `${label} is required` })
      : z.any().optional(),

  MULTI_SELECT: ({ isRequired, label }) =>
    isRequired
      ? z.array(z.string()).min(1, `${label} is required`)
      : z.array(z.string()).optional(),

  UPLOAD_FILE: ({ isRequired, label }) =>
    isRequired
      ? z.any().refine((file) => !!file, { message: `${label} is required` })
      : z.any().optional(),

  GALLERY: ({ isRequired, label }) =>
    isRequired
      ? z.any().refine((file) => !!file, { message: `${label} is required` })
      : z.any().optional(),
};

export function useTemplateForm<
  TFieldName extends string = string,
  TValues extends Record<TFieldName, any> = Record<TFieldName, any>,
>(
  fields: FieldConfig<TFieldName>[],
  templateParams: { id: string | number },
): { form: UseFormReturn<TValues>; template: UseQueryResult<any, Error> } {
  const { id } = templateParams;

  const template = useGetTemplate({
    id,
  });

  const { schema, defaultValues } = useMemo(() => {
    const shape: ZodRawShape = {};
    const initialValues: Partial<Record<TFieldName, any>> = {};

    fields.forEach((field) => {
      const { name, attribute_type, defaultValue } = field;

      const getSchema = zodSchemaMap[attribute_type];
      shape[name] = getSchema ? getSchema(field) : z.string().optional();

      if (defaultValue !== undefined) {
        initialValues[name] = defaultValue;
      } else if (attribute_type === "MULTI_SELECT") {
        initialValues[name] = [];
      } else {
        initialValues[name] = "";
      }
    });

    return {
      schema: z.object(shape),
      defaultValues: initialValues,
    };
  }, [fields]);

  return {
    form: useForm<TValues>({
      resolver: zodResolver(schema),
      mode: "onChange",
      defaultValues,
    }),
    template,
  };
}
