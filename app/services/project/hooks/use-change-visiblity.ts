import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useChangeVisibility({
  options,
}: {
  options?: UseMutationOptions<
    any,
    unknown,
    { marketSlug: string; categoryId: number | string; payload: { visibility_status: string } }
  >;
}) {
  const category = useInjection<Category>(Category);

  return useMutation({
    mutationFn: (args: {
      payload: {
        visibility_status: string;
      };
      categoryId: number | string;
    }) => category.changeVisibility(args),
    ...options,
  });
}
