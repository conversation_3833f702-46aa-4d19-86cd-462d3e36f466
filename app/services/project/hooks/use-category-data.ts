import { useQuery, UseQueryOptions } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useCategoryData({
  options,
  id,
}: {
  options?: UseQueryOptions;
  id: string | number;
}) {
  const categoryService = useInjection<Category>(Category);

  return useQuery({
    queryKey: [QueryKey.CATEGORY_DATA, id],
    queryFn: async () => categoryService.getCategoryWithData({ id }),
    refetchOnMount: true,
    ...options,
  });
}
