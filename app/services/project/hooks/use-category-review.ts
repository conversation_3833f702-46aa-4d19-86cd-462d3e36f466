import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useCategoryReview({
  options,
}: {
  options?: UseMutationOptions<any, unknown, { categoryId: number; payload: { status: string } }>;
}) {
  const category = useInjection<Category>(Category);

  return useMutation({
    mutationFn: (args: { categoryId: number; payload: { status: string } }) =>
      category.categoryReview(args),
    ...options,
  });
}
