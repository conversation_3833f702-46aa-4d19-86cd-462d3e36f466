import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useAddCategory(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      payload: any;
    }
  >,
) {
  const categoryService = useInjection<Category>(Category);

  return useMutation({
    mutationFn: ({ payload }: { payload: any }) => categoryService.addCategory({ payload }),
    ...options,
  });
}
