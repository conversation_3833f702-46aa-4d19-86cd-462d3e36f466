import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useAddAsset(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      id: number;
      payload: any;
    }
  >,
) {
  const categoryService = useInjection<Category>(Category);

  return useMutation({
    mutationFn: (data) => categoryService.addAsset(data),
    ...options,
  });
}
