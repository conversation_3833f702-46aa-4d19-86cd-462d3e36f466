import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { Category } from "~/services/category/category";

export function useCategoryUpdate({
  options,
}: {
  options?: UseMutationOptions<any, unknown, { categoryId: number; payload: any }>;
}) {
  const category = useInjection<Category>(Category);

  return useMutation({
    mutationFn: (args: { categoryId: number; payload: any }) => category.updateCategory(args),
    ...options,
  });
}
