import { useQuery, UseQueryOptions } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";

export function useUnitData({
  options,
  id,
}: {
  options?: UseQueryOptions;
  id: string | number;
}) {
  const productService = useInjection<ProductService>(ProductService);

  return useQuery({
    queryKey: [QueryKey.UNIT_DATA, id],
    queryFn: async () => productService.productDetail(id),
    refetchOnMount: true,
    ...options,
  });
}
