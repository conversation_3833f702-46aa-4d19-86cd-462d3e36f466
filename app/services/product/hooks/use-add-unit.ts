import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";

export function useAddUnit(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      payload: any;
    }
  >,
) {
  const productService = useInjection<ProductService>(ProductService);

  return useMutation({
    mutationFn: ({ payload }: { payload: any }) => productService.createProduct(payload),
    ...options,
  });
}
