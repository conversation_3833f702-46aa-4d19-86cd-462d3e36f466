import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";

export enum ProductStatus {
  HIDDEN = "HIDDEN",
  ACTIVE = "ACTIVE",
}

export function useStatusUpdate(
  options?: UseMutationOptions<any, unknown, { unitId: string; status: ProductStatus }>,
) {
  const productService = useInjection<ProductService>(ProductService);

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ unitId, status }) => productService.updateStatus(unitId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.PRODUCT_LIST] });
    },
    ...options,
  });
}
