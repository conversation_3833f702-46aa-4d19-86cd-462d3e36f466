import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";
import { QueryKey } from "~/context/reactQueryProvider";

import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product/product";

export function useEditUnit(
  options?: UseMutationOptions<
    any,
    unknown,
    {
      unitId: string;
      payload: any;
    }
  >,
) {
  const productService = useInjection<ProductService>(ProductService);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ unitId, payload }: { unitId: string; payload: any }) =>
      productService.updateProduct(unitId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.UNIT_DATA] });
    },
    ...options,
  });
}
