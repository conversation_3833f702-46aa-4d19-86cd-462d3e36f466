import { inject, injectable } from "inversify";

import { RestHelper } from "~/services/rest-helper";

import { BulkImportResponse, GetProductListResponse, ProductServiceInterface } from "./product";
import { ProductStatus } from "./hooks/use-status-update";

@injectable()
export class ProductImpl implements ProductServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  getProductList: (args: {
    /*
      @params: 
      marketplace_product_status: string;
      search: string;
    */
    status?: string;
  }) => Promise<GetProductListResponse> = (args: { status?: string }) => {
    return this.restHelper.get("/api/v1.2/ecommerce/products/", {
      params: {
        ordering: "-marketplace_product__date_submitted",
        ...args,
      },
    });
  };

  createProduct: (args: any) => Promise<GetProductListResponse> = (args: any) => {
    return this.restHelper.post("/api/v1.2/ecommerce/products/", {
      data: args,
    });
  };

  updateProduct: (id: string, args: any) => Promise<GetProductListResponse> = (
    id: string,
    args: any,
  ) => {
    return this.restHelper.put(`/api/v1.2/ecommerce/products/${id}/`, {
      data: args,
    });
  };

  getInterestList: () => Promise<GetProductListResponse> = () => {
    return this.restHelper.get("/api/v1.2/ecommerce/orders/", {
      params: {
        limit: 20,
        show_archived_orders: false,
        refetchNumber: 0,
        ordering: "-created_date",
        date: "",
      },
    });
  };

  bulkUploadProduct: (args: File) => Promise<BulkImportResponse> = async (args: File) => {
    const formData = new FormData();
    formData.append("file", args);
    return await this.restHelper.post("/api/v1/ecommerce/products/bulk-import/", {
      data: formData,
    });
  };

  productDetail: (id: string | number) => Promise<unknown> = async (id: string | number) => {
    return await this.restHelper.get(`/api/v1.2/ecommerce/products/${id}/`);
  };

  uploadUnitAssets: (args: any) => Promise<unknown> = async (args: any) => {
    const formData = new FormData();
    formData.append("image", args.file);
    try {
      const data = await this.restHelper.post(`/api/v1.2/ecommerce/products/images/`, {
        data: formData,
      });
      return data?.id;
    } catch (error) {
      return null;
    }
  };

  deleteProduct: (id: string) => Promise<any> = (id: string) => {
    return this.restHelper.delete(`/api/v1.2/ecommerce/products/${id}/`);
  };

  updateStatus: (id: string, status: ProductStatus) => Promise<any> = (id: string, status: ProductStatus) => {
    return this.restHelper.patch(`/api/v1.2/ecommerce/products/${id}/`, {
      data: {
        status: status,
      },
    });
  };
}
