import { inject, injectable } from "inversify";

import { MerchantDataService } from "../merchant-data/merchant-data";
import { RestHelper } from "../rest-helper";

import { Template } from "./template";

@injectable()
export class TemplateImpl implements Template {
  constructor(
    @inject(RestHelper)
    protected readonly restHelper: RestHelper,
    @inject(MerchantDataService)
    protected readonly merchantDataService: MerchantDataService,
  ) {}

  async getTemplateById({ id }: { id: string | number }): Promise<unknown> {
    const marketPlaceSlug = this.merchantDataService.marketPlace;
    return await this.restHelper.get(
      `/api/v1/marketplaces/${marketPlaceSlug}/inventory-schemas/${id}/`,
    );
  }
}
