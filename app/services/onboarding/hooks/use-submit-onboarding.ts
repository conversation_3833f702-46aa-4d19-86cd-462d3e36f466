import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { OnboardingPayloadType, OnboardingService } from "~/services/onboarding/onboarding";

export function useSubmitOnboarding(options?: UseMutationOptions<any, unknown, OnboardingPayloadType>) {
  const onboardingService = useInjection<OnboardingService>(OnboardingService);

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: OnboardingPayloadType) => onboardingService.submitOnboardingForm(payload),
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: [QueryKey.SUBMIT_ONBAORDING] });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}
