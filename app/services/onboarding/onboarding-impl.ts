import { inject, injectable } from "inversify";

import { RestHelper } from "../rest-helper";
import { OnboardingPayloadType, OnboardingServiceInterface } from "./onboarding";

@injectable()
export class OnboardingServiceImpl implements OnboardingServiceInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  submitOnboardingForm = (payload: OnboardingPayloadType) => {
    return this.restHelper.post("/api/v1/marketplaces/brokerage/sellers/", {
      data: payload,
    });
  };
}
