import { OnboardingServiceImpl } from "./onboarding-impl";

export type OnboardingPayloadType = {
  full_name: string;
  job_title: string;
  work_email: string;
  phone_number: string;
  business_type: string;
  company_name: string;
  company_registration_number: string;
  portfolio_website_url: string;
  comments: string;
};

export interface OnboardingServiceInterface {
  submitOnboardingForm(payload: OnboardingPayloadType): Promise<any>;
}

export type OnboardingService = OnboardingServiceImpl;
export const OnboardingService = Symbol("OnboardingService");
