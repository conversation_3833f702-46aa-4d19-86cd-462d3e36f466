import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { useStore } from "~/store/store";

import {
  MerchantDataFileUploadService,
  MerchantDataFileUploadPayload,
  MerchantDataFileRemovalPayload,
} from "../merchant-data-file-upload";
import { MerchantDataResponse } from "../types";

export function useMerchantDataFileUpload(
  options?: UseMutationOptions<MerchantDataResponse, unknown, MerchantDataFileUploadPayload>,
) {
  const queryClient = useQueryClient();
  const fileUploadService = useInjection<MerchantDataFileUploadService>(
    MerchantDataFileUploadService,
  );
  const setMerchantData = useStore().getState().setMerchantData;

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "file-upload"],
    mutationFn: (payload: MerchantDataFileUploadPayload) => fileUploadService.uploadFiles(payload),
    onSuccess: (data) => {
      // Update store with response data that includes file URLs
      setMerchantData(data);
      // Invalidate and refetch merchant data queries
      queryClient.invalidateQueries({ queryKey: [QueryKey.MERCHANT_DATA] });
    },
    ...options,
  });
}

export function useMerchantDataFileRemoval(
  options?: UseMutationOptions<MerchantDataResponse, unknown, MerchantDataFileRemovalPayload>,
) {
  const queryClient = useQueryClient();
  const fileUploadService = useInjection<MerchantDataFileUploadService>(
    MerchantDataFileUploadService,
  );
  const setMerchantData = useStore().getState().setMerchantData;

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "file-removal"],
    mutationFn: (payload: MerchantDataFileRemovalPayload) => fileUploadService.removeFiles(payload),
    onSuccess: (data) => {
      // Update store with response data that has files removed
      setMerchantData(data);
      // Invalidate and refetch merchant data queries
      queryClient.invalidateQueries({ queryKey: [QueryKey.MERCHANT_DATA] });
    },
    ...options,
  });
}

export function useMerchantFileUpload(options?: UseMutationOptions<unknown, unknown, unknown>) {
  const fileUploadService = useInjection<MerchantDataFileUploadService>(
    MerchantDataFileUploadService,
  );

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "section-file-upload"],
    mutationFn: (payload) => fileUploadService.uploadMediaSectionFiles(payload),
    ...options,
  });
}

export function useBannerFileUpload(options?: UseMutationOptions<unknown, unknown, unknown>) {
  const fileUploadService = useInjection<MerchantDataFileUploadService>(
    MerchantDataFileUploadService,
  );

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "section-banner-upload"],
    mutationFn: (payload) => fileUploadService.uploadBannerSectionFile(payload),
    ...options,
  });
}
