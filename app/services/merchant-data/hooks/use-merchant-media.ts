import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";

import { MerchantDataService } from "../merchant-data";

export function useMerchantMedia({
  objectType,
  options,
}: {
  objectType: string;
  options?: {
    enabled?: boolean;
  };
}) {
  const merchantData = useInjection<MerchantDataService>(MerchantDataService);
  return useQuery({
    queryKey: [QueryKey.MERCHANT_SECTION_DATA, { objectType }],
    queryFn: () => merchantData.getMerchantMedia({ objectType }),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: options?.enabled ?? true,
  });
}
