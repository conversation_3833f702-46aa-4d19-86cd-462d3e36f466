import { useMutation, UseMutationOptions } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";

import { MerchantDataService } from "../merchant-data";

export function useUpdateMerchantMedia(options?: UseMutationOptions<unknown, unknown, unknown>) {
  const merchantDataService = useInjection<MerchantDataService>(MerchantDataService);

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "update"],
    mutationFn: (payload: unknown) => merchantDataService.updateSectionData(payload),

    ...options,
  });
}
