import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";

import { MerchantDataService } from "../merchant-data";

export function useMerchantData(options?: { enabled?: boolean }) {
  const merchantData = useInjection<MerchantDataService>(MerchantDataService);
  return useQuery({
    queryKey: [QueryKey.MERCHANT_DATA],
    queryFn: () => merchantData.getMerchantData(),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: options?.enabled ?? true, // Enable by default now
    staleTime: 2 * 1000,
  });
}
