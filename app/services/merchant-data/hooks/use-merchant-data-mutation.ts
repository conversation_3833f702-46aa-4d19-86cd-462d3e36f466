import { useMutation, UseMutationOptions, useQueryClient } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useInjection } from "~/hooks/use-di";
import { useStore } from "~/store/store";

import { MerchantDataService } from "../merchant-data";
import { MerchantDataPayload, MerchantDataResponse } from "../types";

export function useMerchantDataCreate(
  options?: UseMutationOptions<MerchantDataResponse, unknown, MerchantDataPayload>,
) {
  const queryClient = useQueryClient();
  const merchantDataService = useInjection<MerchantDataService>(MerchantDataService);
  const setMerchantData = useStore().getState().setMerchantData;

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "create"],
    mutationFn: (payload: MerchantDataPayload) => merchantDataService.createMerchantData(payload),
    onSuccess: (data) => {
      setMerchantData(data);
      queryClient.invalidateQueries({ queryKey: [QueryKey.MERCHANT_DATA] });
    },
    ...options,
  });
}

export function useUpdateMerchantMedia(
  options?: UseMutationOptions<MerchantDataResponse, unknown, MerchantDataPayload>,
) {
  const queryClient = useQueryClient();
  const merchantDataService = useInjection<MerchantDataService>(MerchantDataService);
  const setMerchantData = useStore().getState().setMerchantData;

  return useMutation({
    mutationKey: [QueryKey.MERCHANT_DATA, "update"],
    mutationFn: (payload: MerchantDataPayload) => merchantDataService.updateMerchantData(payload),
    onSuccess: (data) => {
      setMerchantData(data);
      queryClient.invalidateQueries({ queryKey: [QueryKey.MERCHANT_DATA] });
    },
    ...options,
  });
}
