import { inject, injectable } from "inversify";

import { RestHelper } from "../rest-helper";
import { StoreService } from "../store-service/store-service-impl";

import { MerchantDataInterface } from "./merchant-data";
import { MerchantDataPayload, MerchantDataResponse, MerchantProfileSection } from "./types";

@injectable()
export class MerchantDataImpl implements MerchantDataInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
    @inject(StoreService)
    private readonly storeService: StoreService,
  ) {}

  async getMerchantData(): Promise<MerchantDataResponse> {
    const merchantData = await this.restHelper.unwrap(
      this.restHelper.get<MerchantDataResponse>(`/api/v1.2/merchant/data/?lang_fallback=true`),
    );
    this.storeService.state.setMerchantData(merchantData);
    return merchantData;
  }

  async getMerchantMedia({
    objectType,
  }: {
    objectType: string;
  }): Promise<MerchantProfileSection[]> {
    return await this.restHelper.unwrap(
      this.restHelper.get<MerchantDataResponse>(
        `/api/v1.2/merchant/sections/?object_type=${objectType}`,
      ),
    );
  }

  get marketPlace(): string {
    return this.storeService.state.merchantData?.marketplace?.slug ?? "brokerage";
  }

  get theme(): unknown {
    return this.storeService.state.merchantData?.global_styles ?? {};
  }

  async createMerchantData(payload: MerchantDataPayload): Promise<MerchantDataResponse> {
    return this.restHelper.unwrap(
      this.restHelper.post<MerchantDataResponse>("/api/v1.2/merchant/data/", {
        data: payload,
      }),
    );
  }

  async updateMerchantData(payload: MerchantDataPayload): Promise<MerchantDataResponse> {
    return this.restHelper.unwrap(
      this.restHelper.patch<MerchantDataResponse>("/api/v1.2/merchant/data/", {
        data: payload,
      }),
    );
  }

  async updateSectionData(payload: unknown): Promise<unknown> {
    return this.restHelper.unwrap(
      this.restHelper.post<MerchantDataResponse>("/api/v1.2/merchant/sections/", {
        data: payload,
      }),
    );
  }
}
