import { inject, injectable } from "inversify";

import { RestHelper } from "../rest-helper";

import { MerchantDataResponse } from "./types";

export interface FileUploadField {
  fieldName: string;
  file: File;
  accept?: string; // file type restrictions like "image/*", "video/*", etc.
}

export interface FileUploadConfig {
  files: FileUploadField[];
  additionalFields?: Record<string, string | boolean | number>;
}

export interface MerchantDataFileUploadPayload {
  config: FileUploadConfig;
}

export interface MerchantDataFileUploadInterface {
  uploadFiles(payload: MerchantDataFileUploadPayload): Promise<MerchantDataResponse>;
  removeFiles(payload: MerchantDataFileRemovalPayload): Promise<MerchantDataResponse>;
}

export interface UploadSection {
  config: { files: File[]; additionalFields: Record<string, string> };
}

export interface MerchantDataFileRemovalPayload {
  fieldsToRemove: Array<{
    fileFieldName: string; // e.g., "navbar_logo", "icon"
    showFieldName: string; // e.g., "show_navbar_logo", "show_icon"
  }>;
}

@injectable()
export class MerchantDataFileUploadImpl implements MerchantDataFileUploadInterface {
  constructor(
    @inject(RestHelper)
    private readonly restHelper: RestHelper,
  ) {}

  async uploadFiles(payload: MerchantDataFileUploadPayload): Promise<MerchantDataResponse> {
    const formData = new FormData();
    const { config } = payload;

    // Add files to form data
    config.files.forEach(({ fieldName, file }) => {
      formData.append(fieldName, file);
    });

    // Add additional fields (non-file data)
    if (config.additionalFields) {
      Object.entries(config.additionalFields).forEach(([key, value]) => {
        formData.append(key, value.toString());
      });
    }

    return this.restHelper.unwrap(
      this.restHelper.patch<MerchantDataResponse>("/api/v1.2/merchant/data/", {
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    );
  }

  async removeFiles(payload: MerchantDataFileRemovalPayload): Promise<MerchantDataResponse> {
    const formData = new FormData();

    // Add empty file fields and set show parameters to false
    payload.fieldsToRemove.forEach(({ fileFieldName, showFieldName }) => {
      // Set file field to empty (this removes the file)
      formData.append(fileFieldName, "");
      // Set show parameter to false
      formData.append(showFieldName, "false");
    });

    return this.restHelper.unwrap(
      this.restHelper.patch<MerchantDataResponse>("/api/v1.2/merchant/data/", {
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    );
  }

  async uploadMediaSectionFiles(payload: UploadSection): Promise<unknown> {
    const formData = new FormData();
    const { config } = payload;
    const { additionalFields = {}, ...rest } = config;

    const files = rest?.files;

    if (!files.length) throw new Error("No files provided");

    const sectionType = additionalFields.section_type?.toString();
    const objectType = additionalFields.object_type?.toString();

    formData.append("section_type", sectionType);
    formData.append("object_type", objectType);

    if (additionalFields?.titleEn) {
      formData.append("objects[0][title_en]", additionalFields?.titleEn);
    }

    if (additionalFields?.titleAr) {
      formData.append("objects[0][title_ar]", additionalFields?.titleAr);
    }

    if (additionalFields?.descriptionEn) {
      formData.append("objects[0][description_en]", additionalFields?.descriptionEn);
    }

    if (additionalFields?.descriptionAr) {
      formData.append("objects[0][description_ar]", additionalFields?.descriptionAr);
    }

    files.forEach((file, index) => {
      formData.append(`objects[0][images][${index}][image]`, file, file.name);
      formData.append(`objects[0][images][${index}][order]`, String(index));
    });

    return this.restHelper.unwrap(
      this.restHelper.post<MerchantDataResponse>("/api/v1.2/merchant/sections/", {
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    );
  }

  async uploadBannerSectionFile(payload: UploadSection): Promise<unknown> {
    const formData = new FormData();
    const { config } = payload;
    const { additionalFields = {}, ...rest } = config;

    const files = rest?.files;

    if (!files.length) throw new Error("No files provided");

    const sectionType = additionalFields.section_type?.toString();
    const objectType = additionalFields.object_type?.toString();

    formData.append("section_type", sectionType);
    formData.append("object_type", objectType);

    if (additionalFields?.titleEn) {
      formData.append("objects[0][title_en]", additionalFields?.titleEn);
    }

    if (additionalFields?.titleAr) {
      formData.append("objects[0][title_ar]", additionalFields?.titleAr);
    }

    files.forEach((file) => {
      formData.append(`objects[0][image]`, file, file.name);
    });

    return this.restHelper.unwrap(
      this.restHelper.post<MerchantDataResponse>("/api/v1.2/merchant/sections/", {
        data: formData,
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }),
    );
  }
}

export const MerchantDataFileUploadService = Symbol("MerchantDataFileUploadService");
export type MerchantDataFileUploadService = MerchantDataFileUploadImpl;
