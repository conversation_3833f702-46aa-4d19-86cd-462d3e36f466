import {
  MerchantDataFileUploadPayload,
  FileUploadConfig,
  FileUploadField,
  MerchantDataFileRemovalPayload,
} from "./merchant-data-file-upload";
import {
  MerchantDataPayload,
  MerchantDataResponse,
  MerchantProfileSection,
  ProfileFormData,
} from "./types";

export function mapProfileFormToMerchantData(formData: ProfileFormData): MerchantDataPayload {
  return {
    // store_name: formData.store_name,
    store_name_en: formData.store_name_en,
    store_name_ar: formData.store_name_ar,
    address: formData.address,
    // address_en: formData.address_en,
    // address_ar: formData.address_ar,
    email: formData.email,
    // description: formData.description,
    description_en: formData.description_en,
    description_ar: formData.description_ar,
    mission_statement: formData.mission_statement,
    mission_statement_en: formData.mission_statement_en,
    mission_statement_ar: formData.mission_statement_ar,
    vision_statement: formData.vision_statement,
    vision_statement_en: formData.vision_statement_en,
    vision_statement_ar: formData.vision_statement_ar,
    slogan_en: formData.slogan_en,
    slogan_ar: formData.slogan_ar,
    website: formData.website,
    phone: formData.phone || "",
    // whatsapp_phone: formData.whatsapp_phone || formData.phone || "",
    // customer_support_email: formData.customer_support_email || formData.email,
    // contact_email: formData.contact_email || formData.email,
    // social_links: formData.social_links,
    // Default values that may need to be expanded based on form requirements
    // time_zone: "UTC",
    // category: "",
    // follow_business_hours: false,
    // seller_category: undefined,
    // business_hours: undefined,
  };
}

/**
 * Generic function to create file upload configuration
 * @param files - Array of file upload configurations
 * @param additionalFields - Additional non-file fields to include
 */
export function createFileUploadConfig(
  files: FileUploadField[],
  additionalFields?: Record<string, string | boolean | number>,
): FileUploadConfig {
  return {
    files,
    additionalFields,
  };
}

/**
 * Create upload configuration for profile logo/icon
 */
export function createProfileLogoUploadConfig(logoFile: File): FileUploadConfig {
  return createFileUploadConfig(
    [
      { fieldName: "navbar_logo", file: logoFile, accept: "image/*" },
      { fieldName: "icon", file: logoFile, accept: "image/*" },
    ],
    { show_navbar_logo: true },
  );
}

/**
 * Create upload configuration for banner images
 */
export function createBannerUploadConfig(bannerFile: File[]) {
  return {
    files: bannerFile,
    additionalFields: {
      section_type: "merchant_profile",
      object_type: "merchant_profile_banner",
    },
  };
}

export function createMediaUploadConfig({
  media,
  ...rest
}: {
  media: File[];
  titleEn: string;
  titleAr: string;
  descriptionEn: string;
  descriptionAr: string;
}) {
  return {
    files: media,
    additionalFields: {
      section_type: "merchant_profile",
      object_type: "merchant_profile_gallery",
      ...rest,
    },
  };
}

/**
 * Create upload configuration for video content
 */
export function createVideoUploadConfig(
  videoFile: File,
  fieldName = "promotional_video",
): FileUploadConfig {
  return createFileUploadConfig([{ fieldName, file: videoFile, accept: "video/*" }]);
}

/**
 * Create upload configuration for thumbnails
 */
export function createThumbnailUploadConfig(
  thumbnailFile: File,
  fieldName = "thumbnail",
): FileUploadConfig {
  return createFileUploadConfig([{ fieldName, file: thumbnailFile, accept: "image/*" }]);
}

export function mapProfileFormToFileUpload(
  formData: ProfileFormData,
): MerchantDataFileUploadPayload | null {
  if (
    formData.logo &&
    Array.isArray(formData.logo) &&
    formData.logo.length > 0 &&
    formData.logo[0] instanceof File
  ) {
    return {
      config: createProfileLogoUploadConfig(formData.logo[0]),
    };
  }

  return null;
}

export function mapProfileBannerFormToFileUpload(formData: ProfileFormData) {
  if (
    formData.banner_image &&
    Array.isArray(formData.banner_image) &&
    formData.banner_image.length > 0 &&
    formData.banner_image[0] instanceof File
  ) {
    return {
      config: createBannerUploadConfig(formData.banner_image),
    };
  }

  return null;
}

export async function mapProfileGalleryFormToFileUpload(formData: ProfileFormData): Promise<{
  config: {
    media: File[];
    titleEn: string;
    titleAr: string;
    descriptionEn: string;
    descriptionAr: string;
  };
} | null> {
  if (
    formData.media_gallery &&
    Array.isArray(formData.media_gallery) &&
    formData.media_gallery.length > 0
  ) {
    const media = await Promise.all(
      formData.media_gallery.map((file) =>
        file instanceof File ? file : urlToFileWithType(file?.url),
      ),
    );

    return {
      config: createMediaUploadConfig({
        media: media.filter(Boolean) as File[],
        titleEn: formData.gallery_title_en,
        titleAr: formData.gallery_title_ar,
        descriptionEn: formData.description_en,
        descriptionAr: formData.description_ar,
      }),
    };
  }

  return null;
}

export function hasFilesToUpload<T extends Record<string, any>>(formData: T): boolean {
  return Object.values(formData).some(
    (value) =>
      Array.isArray(value) && value.length > 0 && value.some((item) => item instanceof File),
  );
}

const urlToFile = (url: string) => {
  if (!url) return;
  const fileName = url.split("/").pop() || "";
  return new File([url], fileName);
};

export const urlToFileWithType = async (url: string): Promise<File | undefined> => {
  if (!url) return;

  const response = await fetch(url);
  const blob = await response.blob();
  const fileName = url.split("/").pop() || "file";

  return new File([blob], fileName, { type: blob.type });
};

export function mapMerchantDataToFormData(
  apiData: MerchantDataResponse & {
    merchantGallery: MerchantProfileSection[];
    merchantBanner: MerchantProfileSection[];
  },
): ProfileFormData {
  const logo = apiData.icon || apiData.navbar_icon || "";

  const mediaGalleryConfig = apiData?.merchantGallery?.[0]?.objects?.[0];

  const renderMediaConfig = mediaGalleryConfig ?? apiData?.media_section_gallery;

  const mediaGalleryImages = mediaGalleryConfig
    ? mediaGalleryConfig?.images?.map((i) => ({ url: i?.image }))
    : apiData?.media_gallery;

  const bannerImages = apiData?.merchantBanner?.[0]?.objects?.[0]
    ? [{ url: apiData?.merchantBanner?.[0]?.objects?.[0]?.image }]
    : apiData?.banner_image;

  return {
    store_name: apiData.store_name_en || "",
    store_name_en: apiData.store_name_en || "",
    store_name_ar: apiData.store_name_ar || "",
    address: apiData.address || "",
    address_en: apiData.address_en || "",
    address_ar: apiData.address_ar || "",
    email: apiData.email || "",
    description: apiData.description_en || "",
    description_en: apiData.description_en || "",
    description_ar: apiData.description_ar || "",
    mission_statement: apiData.mission_statement_en || "",
    mission_statement_en: apiData.mission_statement_en || "",
    mission_statement_ar: apiData.mission_statement_ar || "",
    vision_statement: apiData.vision_statement_en || "",
    vision_statement_en: apiData.vision_statement_en || "",
    vision_statement_ar: apiData.vision_statement_ar || "",
    slogan: apiData.slogan_en || "",
    slogan_en: apiData.slogan_en || "",
    slogan_ar: apiData.slogan_ar || "",
    website: apiData.website || "",
    phone: apiData.phone || "",
    logo: logo
      ? [
          {
            url: logo,
          },
        ]
      : [],
    color: apiData.global_styles?.brand_color || apiData.palette?.brand_color || "#ffffff",
    media_section_gallery: renderMediaConfig ? "true" : "false",
    mission_and_vision: apiData?.vision_statement ? "true" : "false",
    media_gallery: mediaGalleryImages ?? [],
    banner_image: bannerImages ?? [],
    gallery_title_en: mediaGalleryConfig?.title_en ?? apiData?.gallery_title_en,
    gallery_title_ar: mediaGalleryConfig?.title_ar ?? apiData?.gallery_title_ar,
    gallery_description_en: mediaGalleryConfig?.description_en ?? apiData?.gallery_description_en,
    gallery_description_ar: mediaGalleryConfig?.description_ar ?? apiData?.gallery_description_ar,
  };
}

/**
 * Create file removal configuration for profile logo
 */
export function createProfileLogoRemovalConfig(): MerchantDataFileRemovalPayload {
  return {
    fieldsToRemove: [
      {
        fileFieldName: "icon",
        showFieldName: "show_icon",
      },
      {
        fileFieldName: "navbar_icon",
        showFieldName: "show_navbar_logo",
      },
    ],
  };
}

/**
 * Check if logo should be removed (file was cleared/deleted)
 */
export function shouldRemoveLogo(
  formData: ProfileFormData,
  originalData: ProfileFormData,
): boolean {
  // Check if logo was previously present but is now cleared
  const hadLogo =
    originalData.logo && Array.isArray(originalData.logo) && originalData.logo.length > 0;

  // Handle logo as either File array or single value
  const currentLogo = formData.logo;
  let hasLogo = false;

  if (Array.isArray(currentLogo)) {
    hasLogo = currentLogo.length > 0 && currentLogo[0] instanceof File;
  } else if (currentLogo instanceof File) {
    hasLogo = true;
  } else if (typeof currentLogo === "string") {
    hasLogo = currentLogo.length > 0;
  }

  return Boolean(hadLogo && !hasLogo);
}

/**
 * Map form data to file removal payload
 */
export function mapProfileFormToFileRemoval(
  formData: ProfileFormData,
  originalData: ProfileFormData,
): MerchantDataFileRemovalPayload | null {
  if (shouldRemoveLogo(formData, originalData)) {
    return createProfileLogoRemovalConfig();
  }

  return null;
}
