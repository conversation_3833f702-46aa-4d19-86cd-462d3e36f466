import { FileSource } from "~/hooks/use-file-url";

export interface MerchantDataPayload {
  store_name?: string;
  store_name_en: string;
  store_name_ar?: string;
  address: string;
  address_en?: string;
  address_ar?: string;
  email: string;
  description?: string;
  description_en: string;
  description_ar?: string;
  mission_statement?: string;
  mission_statement_en?: string;
  mission_statement_ar?: string;
  vision_statement?: string;
  vision_statement_en?: string;
  vision_statement_ar?: string;
  slogan?: string;
  slogan_en: string;
  slogan_ar?: string;
  website?: string;
  phone: string;
  whatsapp_phone?: string;
  customer_support_email?: string;
  contact_email?: string;
  time_zone?: string;
  category?: string;
  social_links?: {
    instagram?: string;
    tiktok?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    viber_phone?: string;
  };
  business_hours?: Array<{
    id?: number;
    weekday: number;
    weekday_display: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
    is_all_day: boolean;
  }>;
  follow_business_hours?: boolean;
  seller_category?: number;
}

export interface MerchantDataResponse {
  [x: string]: any;
  id: number;
  store_name: string;
  store_name_en?: string;
  store_name_ar?: string;
  slug: string;
  slug_update_counter: number;
  address: string;
  address_en?: string;
  address_ar?: string;
  contact_email: string;
  is_email_verified: boolean;
  phone: string;
  whatsapp_phone: string;
  country: string;
  description: string;
  description_en?: string;
  description_ar?: string;
  icon?: string;
  mission_statement?: string;
  mission_statement_en?: string;
  mission_statement_ar?: string;
  vision_statement?: string;
  vision_statement_en?: string;
  vision_statement_ar?: string;
  slogan?: string;
  slogan_en: string;
  slogan_ar?: string;
  website?: string;
  store_type: string;
  payment_type: string;
  payment_types: string[];
  email: string;
  sections: number[];
  payments: Record<string, any>;
  is_min_spend_active: boolean;
  min_spending: string;
  max_spending: string;
  inventory_tracking_enabled: boolean;
  navbar_icon?: string;
  navbar_icons: {
    "16"?: string | null;
    "32"?: string | null;
    "180"?: string | null;
  };
  navbar_logo?: string;
  navbar_logo_position: string;
  navbar_size: string;
  navbar_position: string;
  language: string;
  time_zone: string;
  currency: number;
  custom_domains: string[];
  currency_code: string;
  show_navbar_logo: boolean;
  google_analytics_code?: string | null;
  google_analytics_code_alt?: string | null;
  google_tag_manager_container_id?: string | null;
  meta_pixel_id?: string | null;
  palette?: {
    id: number;
    name: string;
    brand_color: string;
    top_bar_color: string;
    top_bar_text_color: string;
    background_color: string;
    image?: string | null;
    order: number;
  } | null;
  global_styles?: {
    brand_color: string;
    top_bar_color: string;
    top_bar_text_color: string;
    background_color: string;
    side_spacing: number;
    style_type: string;
    corner_type: string;
    navigation_type: string;
    description_type: string;
    collections_title: string;
    tab_bar_display: string;
  } | null;
  product_page_settings?: any;
  seller_category?: number | null;
  seller_category_data?: {
    id: number;
    title: string;
    emoji: string;
    icon?: string | null;
  } | null;
  share_text: string;
  privacy_policy: string;
  refund_policy: string;
  shipping_policy: string;
  terms_of_service: string;
  customer_support_number: string;
  customer_support_email: string;
  shopping_cart_note?: string | null;
  social_links?: {
    instagram?: string;
    tiktok?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    viber_phone?: string;
  } | null;
  store_url: string;
  store_preview_url: string;
  follow_business_hours: boolean;
  is_store_open: boolean;
  closed_until?: string | null;
  is_onboarded: boolean;
  is_test_merchant: boolean;
  font_styles?: any;
  theme?: any;
  is_palette_custom: boolean;
  is_auto_archive_order_enabled: boolean;
  is_fnb?: boolean | null;
  is_bookable: boolean;
  ecom_plan: any[];
  auto_catalog_usage: string;
  is_view_only_link_active: boolean;
  marketplace: {
    slug: string;
    title: string;
    product_reviews_enabled: boolean;
    admin_product_listing_review_required: boolean;
    admin_category_listing_review_required: boolean;
    languages: Array<{
      code: string;
      name: string;
      name_local: string;
      name_translated: string;
      bidi: boolean;
    }>;
  };
}

export interface ProfileFormData {
  store_name?: string;
  store_name_en: string;
  store_name_ar?: string;
  address: string;
  address_en: string;
  address_ar?: string;
  email: string;
  description?: string;
  description_en: string;
  description_ar?: string;
  mission_statement?: string;
  mission_statement_en?: string;
  mission_statement_ar?: string;
  vision_statement?: string;
  vision_statement_en?: string;
  vision_statement_ar?: string;
  slogan?: string;
  slogan_en: string;
  slogan_ar?: string;
  website?: string;
  phone?: string;
  whatsapp_phone?: string;
  customer_support_email?: string;
  contact_email?: string;
  logo?: FileSource[];
  color?: string;
  social_links?: {
    instagram?: string;
    tiktok?: string;
    facebook?: string;
    twitter?: string;
    youtube?: string;
    linkedin?: string;
    viber_phone?: string;
  };
  banner_image?: File | string | File[];
  media_gallery?: File | string | File[];
  gallery_title_en?: string;
  gallery_title_ar?: string;
  gallery_description_en?: string;
  gallery_description_ar?: string;
}

export interface SectionStyle {
  product_view: string;
  internal_spacing: number;
  top_spacing: number;
  bottom_spacing: number;
  alignment: string;
  aspect_ratio: string;
  background: string;
  background_color: string;
  button_color: string;
  button_text_color: string;
  collection_name_overlay: boolean;
  columns_desktop: number;
  columns_mobile: number;
  corner_type: string;
  desc_color: string;
  desc_font_size: string;
  enabled_quick_add_to_cart: boolean;
  fill_color: string;
  height: number;
  height_size: string;
  image_mask: string;
  image_scalling: string;
  is_vertical_product_image: boolean;
  line_color: string;
  line_type: string;
  position: string;
  rows: string;
  show_names: boolean;
  side_spacing: number;
  style_type: string;
  text_color: string;
  title_color: string;
  title_font_size: string;
  use_global_styles: boolean;
}

export interface ImageObject {
  id: number;
  image: string;
  image_alt: string | null;
  order: number;
}

export interface SectionObject {
  id: number;
  title: string;
  title_en: string;
  title_ar: string;
  description: string;
  description_en: string;
  description_ar: string;
  images: ImageObject[];
  order: number;
}

export interface MerchantProfileSection {
  id: number;
  section_type: string;
  object_type: string;
  icon: string | null;
  settings: any;
  styles: SectionStyle;
  objects: SectionObject[];
  order: number;
}
