// Service exports
export { MerchantDataService, type MerchantDataInterface } from "./merchant-data";
export { MerchantDataImpl } from "./merchant-data-impl";
export {
  MerchantDataFileUploadService,
  MerchantDataFileUploadImpl,
  type MerchantDataFileUploadInterface,
  type MerchantDataFileUploadPayload,
  type MerchantDataFileRemovalPayload,
  type FileUploadConfig,
  type FileUploadField,
} from "./merchant-data-file-upload";

// Type exports
export type { MerchantDataPayload, MerchantDataResponse, ProfileFormData } from "./types";

// Hook exports
export { useMerchantData } from "./hooks/use-merchant-data";
export { useMerchantDataCreate, useUpdateMerchantMedia } from "./hooks/use-merchant-data-mutation";
export {
  useMerchantDataFileUpload,
  useMerchantDataFileRemoval,
} from "./hooks/use-merchant-data-file-upload";

// Mapper exports
export {
  mapProfileFormToMerchantData,
  mapMerchantDataToFormData,
  mapProfileFormToFileUpload,
  mapProfileFormToFileRemoval,
  hasFilesToUpload,
} from "./merchant-data-mapper";
