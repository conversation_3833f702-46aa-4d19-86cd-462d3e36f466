import { MerchantDataImpl } from "./merchant-data-impl";
import { MerchantDataPayload, MerchantDataResponse, MerchantProfileSection } from "./types";

export interface MerchantDataInterface {
  getMerchantData(): Promise<MerchantDataResponse>;
  get marketPlace(): string;
  get theme(): unknown;
  getMerchantMedia({ objectType }: { objectType: string }): Promise<MerchantProfileSection[]>;
  createMerchantData(payload: MerchantDataPayload): Promise<MerchantDataResponse>;
  updateMerchantData(payload: MerchantDataPayload): Promise<MerchantDataResponse>;
  updateSectionData(payload: unknown): Promise<unknown>;
}

export type MerchantDataService = MerchantDataImpl;
export const MerchantDataService = Symbol("MerchantDataImpl");
