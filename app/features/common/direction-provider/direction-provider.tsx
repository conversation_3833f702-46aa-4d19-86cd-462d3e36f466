import { useTheme } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";
import { ReactNode } from "react";

export type Direction = "ltr" | "rtl";

type DirectionProviderProps = {
  children: ReactNode;
  dir?: Direction;
};

const DirectionProvider = ({ dir, children }: DirectionProviderProps) => {
  const theme = useTheme() as AppTheme;

  return (
    <div css={{ width: "100%" }} dir={dir ?? theme?.rds?.direction}>
      {children}
    </div>
  );
};

DirectionProvider.displayName = "DirectionProvider";

export default DirectionProvider;
