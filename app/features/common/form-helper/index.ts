import isEmpty from "lodash/isEmpty";
import { FieldErrors, FieldValues } from "react-hook-form";

export const getFormStatusMessage = <T extends FieldValues>(
  errors: FieldErrors<T>,
  touchedFields: Partial<Record<keyof T, boolean>>,
): string => {
  const hasErrors = !isEmpty(errors);
  const hasTouched = !isEmpty(touchedFields);

  if (errors.button?.type === "failedUnitAdd") return "Failed to create unit";
  if (hasErrors) return "Fix the errors marked in the form before saving.";
  if (hasTouched) return "You have unsaved changes.";
  return "No changes to be saved.";
};

export const formatSectionHeading = (section: string): string => {
  return section
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};
