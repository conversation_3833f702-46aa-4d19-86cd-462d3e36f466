import { useNavigate, useParams } from "@remix-run/react";
import { RDSCard } from "@roshn/ui-kit";

import { useAppPath } from "~/hooks/use-app-path";
import { AppPaths } from "~/utils/app-paths";

export const UnitCard = ({ unitDetails }: any) => {
  const { propertyType, price, status, images, features, unitId } = unitDetails;
  const generatePath = useAppPath();
  const navigate = useNavigate();
  const { id } = useParams();
  const cardData = {
    key: 1,
    media: "onTop",
    mediaPadding: false,
    contentPadding: false,
    cardContentProps: {
      direction: "horizontal",
      topBar: false,
      bgPattern: false,
      mediaCarouselProps: {
        aspectRatio: "1 / 1",
        images,
        tags: [{ label: status, type: "success" }],
        carouselCaret: false,
        bottomNavigation: true,
      },
      bottomBar: true,
      bottomBarProps: {
        propertyHeader: {
          type: "propertyHeader",
          label: `${price}`,
          nestedContent: false,
          cardContentSameLevel: {
            type: "nestedLabel",
            label: propertyType,
            divider: false,
          },
        },
        propertyDescription: [
          {
            type: "propertyFeatures",
            nestedContent: true,
            label: propertyType,
            cardContentPropertyLabel: {
              type: "label",
              label: "Features",
              divider: false,
            },
            cardContentPropertyNested: [...features],
          },
        ],
      },
    },
  };
  return (
    <div
      css={{ cursor: "pointer" }}
      onClick={() => navigate(generatePath(AppPaths.editUnit, { id, unitId }))}
    >
      <RDSCard {...cardData} />
    </div>
  );
};
