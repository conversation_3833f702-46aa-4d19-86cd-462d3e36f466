import { css, useTheme } from "@emotion/react";
import { useNavigate, useParams } from "@remix-run/react";
import {
  RDSTagInteractive,
  RDSButton,
  AppTheme,
  RDSSearchInput,
  RDSModal,
  RDSUploadFile,
  RDSAssetWrapper,
  RDSBanner,
  RDSEmptyState,
} from "@roshn/ui-kit";
import { useEffect, useMemo, useState } from "react";

import { createSvg } from "~/components/svgs";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { useMoneyFormatter } from "~/hooks/use-money-formatter";
import { ProductService } from "~/services/product/product";
import { AppPaths } from "~/utils/app-paths";

import { UnitCard } from "./unit-card";

const Upload = createSvg(() => import("~/assets/icons/upload.svg"));

const featuresMap = ["bedrooms", "bathrooms", "gross_floor_area_sqm"];
const keyMapForFeatures = {
  bedrooms: "Bedroom",
  bathrooms: "Bathroom",
  gross_floor_area_sqm: "sqm",
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      background: theme?.rds?.color.background.brand.secondary.inverse.default,
      width: "100%",
    }),
  header: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingBottom: theme?.rds?.dimension["200"],
    }),
  ctaContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension["200"],
    }),
  searchInput: (theme: AppTheme) =>
    css({
      gap: theme.rds.dimension["200"],
    }),
  tagContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      marginTop: theme.rds.dimension["300"],
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
  }),
  errorBanner: css({
    position: "fixed",
    top: "0",
    left: "0",
    right: "0",
    zIndex: "1000",
    width: "100%",
  }),
  cardContainer: css({
    display: "grid",
    gridGap: "1rem",
    gridTemplateColumns: "repeat(auto-fill, minmax(340px, 1fr))",
    justifyContent: "start",
    width: "100%",
  }),
  emptyStateWrapper: css({
    maxWidth: "30rem",
    margin: "auto",
  }),
};

const AddUnits = ({ onSuccess }: { onSuccess: () => void }) => {
  const [uploadModal, setUploadModal] = useState(false);
  const [inpFile, setInpFile] = useState<unknown>();
  const [loading, setLoading] = useState(false);
  const [uploadFailed, setUploadFailed] = useState(false);
  const productService = useInjection<ProductService>(ProductService);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    setInpFile(file);
    document.getElementsByClassName("roshn-boilerplate-fe-1jke4yk")[0].remove();
    if (!file) return;
  };

  const handelCloseModal = () => {
    setUploadModal(false);
    setInpFile(null);
  };

  const handleUploadUnit = async () => {
    setLoading(true);
    const data = await productService.bulkUploadProduct(inpFile as File);
    setLoading(false);
    handelCloseModal();
    setUploadFailed(data?.errors.length > 0);
  };

  return (
    <>
      {uploadFailed && (
        <div css={styles.errorBanner}>
          <RDSBanner
            title="Upload Failed"
            description="We couldn’t upload your file. Please check that it’s a XLSX under 5MB and formatted correctly."
            appearance="danger"
            isDismissible
          />
        </div>
      )}
      <RDSButton
        css={{ textTransform: "none" }}
        variant="secondary"
        size="lg"
        text="IMPORT UNITS"
        data-testid="upload-button"
        onClick={() => setUploadModal(true)}
        leadIcon={
          <RDSAssetWrapper>
            <Upload />
          </RDSAssetWrapper>
        }
      />
      <RDSModal
        headerProps={{
          label: "Upload File",
          type: "centred",
        }}
        showContent
        showDescription
        description="Add multiple units in bulk by uploading a CSV file. Ensure your file follows the required format to correctly import property details like unit id, status, project and price availability."
        isOpen={uploadModal}
        content={
          <RDSUploadFile
            onChange={(e) => handleFileChange(e)}
            onDismissHandler={() => setInpFile(null)}
            accept=".xlsx"
          />
        }
        footer
        buttonsGroup={{
          buttons: [
            <RDSButton
              variant="primary"
              onClick={() => handleUploadUnit()}
              text="SUBMIT FILES"
              disabled={!inpFile}
              loading={loading}
              key="enrollment"
            />,
            <RDSButton
              variant="secondary"
              onClick={handelCloseModal}
              text="BACK TO INVENTORY"
              key="continue"
            />,
          ],
          direction: "vertical",
        }}
      />
    </>
  );
};

export default function UnitListSection({
  activeTag,
  setActiveTag,
  search,
  setSearch,
  searchParam,
  setSearchParam,
  productList,
  refetch,
  tagData,
}: {
  activeTag: string;
  setActiveTag: (tag: string) => void;
  search: { searchQuery: string };
  setSearch: (search: { searchQuery: string }) => void;
  searchParam: { search: string };
  setSearchParam: (searchParam: { search: string }) => void;
  productList: any;
  refetch: () => void;
  tagData: { label: string; state: string; status: string }[];
}) {
  const theme = useTheme() as AppTheme;
  const generatePath = useAppPath();
  const navigate = useNavigate();
  const { id } = useParams();

  const formatMoney = useMoneyFormatter();

  const responseUnitData = useMemo(() => {
    const unitResults = productList?.results.map((data) => {
      return {
        propertyType: data?.custom_attributes.find((type) => type.label === "property_type")?.value,
        price: formatMoney(data?.price, { numberOfDigits: 0, usingCode: false }),
        status: data?.marketplace_product?.status_display,
        images: data?.images.map(
          (imageData: { id: string; image: string; order: number }) => imageData.image,
        ),
        features: data.custom_attributes
          .map(({ label, value }: { label: string; value: string }, index: number) => {
            if (!featuresMap.includes(label)) return {};
            return {
              type: "nestedLabel",
              label: `${value} ${keyMapForFeatures[label]}`,
              divider: index <= 3,
            };
          })
          .filter((unit) => Object.keys(unit).length > 0),
        unitId: data?.id,
      };
    });
    return unitResults;
  }, [productList]);

  return (
    <div css={styles.wrapper}>
      <div css={styles.header}>
        <div css={{ ...theme?.rds?.typographies?.heading?.emphasis?.h4 }}>
          Listed units ({productList?.count})
        </div>
        <div css={styles.ctaContainer}>
          <AddUnits
            onSuccess={() => {
              refetch();
            }}
          />
          <RDSButton
            size="lg"
            text="+ ADD UNIT"
            onClick={() => navigate(generatePath(AppPaths.addUnit, { id }))}
          />
        </div>
      </div>
      <div style={{ marginBottom: theme.rds.dimension["400"] }}>
        <RDSSearchInput
          type="text"
          placeholder="Search by name, city, status..."
          css={styles.searchInput}
          onChange={(e) => {
            setSearch({ searchQuery: e.target.value });
          }}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              setSearchParam({ search: search.searchQuery });
            }
          }}
          onClearSearchInput={() => {
            setSearch({ searchQuery: "" });
            setSearchParam({ search: "" });
          }}
          value={search.searchQuery}
        />
        <div css={styles.tagContainer}>
          {tagData.map((tag) => (
            <RDSTagInteractive
              key={tag.label}
              size="md"
              label={tag.label}
              state={tag.label === activeTag ? "active" : "default"}
              onClick={() => setActiveTag(tag.label)}
            />
          ))}
        </div>
      </div>
      {productList?.results?.length ? (
        <div css={styles.cardContainer}>
          {responseUnitData.map((unitData) => (
            <UnitCard unitDetails={unitData} />
          ))}
        </div>
      ) : (
        <div css={styles.emptyStateWrapper}>
          <RDSEmptyState
            heading="You don’t have any listed units"
            appearance="danger"
            size="md"
            showMedia={false}
            body="Units you create will appear here. Add new units now to fill up your list."
            buttons={[
              {
                text: "ADD YOUR FIRST UNIT",
                variant: "primary",
                onClick: () => navigate(generatePath(AppPaths.addUnit, { id })),
              },
            ]}
          />
        </div>
      )}
    </div>
  );
}
