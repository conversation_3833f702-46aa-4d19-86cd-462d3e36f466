import { Control, UseFormGetValues } from "react-hook-form";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import { Section } from "~/components/section/section";
import { profileFields } from "~/pages/profile/field-config";

// Type definition for profile field
interface ProfileField {
  attribute_type:
    | "TEXT"
    | "EDITOR"
    | "UPLOAD_FILE"
    | "COLOR_PICKER"
    | "PHONE_NUMBER"
    | "TEXTAREA"
    | "MULTI_SELECT"
    | "NUMBER"
    | "IMAGE"
    | "DATE"
    | "BOOLEAN"
    | "UPLOAD"
    | "COUNTER"
    | "SELECT"
    | "PERCENTAGE"
    | "CURRENCY"
    | "COMPOSITE"
    | "PAYMENT";
  name: string;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
  helperText?: string;
  order?: number;
  caption?: string;
  defaultValue?: string;
  width?: string;
  meta?: {
    visible?: { equals?: string; field: string };
  };
}

function groupAndSortFieldsByOrder(fields: ProfileField[]): ProfileField[][] {
  // Assign missing order values
  const fieldsWithOrder = fields.map((field, index) => ({
    ...field,
    order: field.order ?? index,
  }));

  // Group fields by order
  const groupedByOrder = fieldsWithOrder.reduce(
    (acc, field) => {
      const order = field.order!;
      acc[order] = acc[order] || [];
      acc[order].push(field);
      return acc;
    },
    {} as Record<number, ProfileField[]>,
  );

  // Sort groups by order and return as array
  return Object.values(groupedByOrder).sort((a, b) => a[0].order! - b[0].order!);
}

/**
 * Renders grouped fields with proper layout
 * Multiple fields with same order are rendered horizontally
 * Single fields are rendered vertically
 */

const toRender = (field: ProfileField, values: Record<string, string>) => {
  const dependentField = field?.meta?.visible?.field ?? null;
  const dependentValue = field?.meta?.visible?.equals ?? null;

  return dependentField ? dependentValue === values[dependentField] : true;
};

function renderFieldGroups(
  fieldGroups: ProfileField[][],
  control: Control,
  values = {},
): JSX.Element[] {
  return fieldGroups.map((group, idx) => {
    const isMultipleFields = group.length > 1;
    const containerStyle = isMultipleFields
      ? { display: "flex", flexDirection: "row" as const, gap: "10px" }
      : { ...(group[0].width && { width: group[0].width }) };

    return (
      <div key={group[0].order} style={containerStyle}>
        {group.map((field) => {
          const renderValidity = toRender(field, values);

          if (!renderValidity) return null;

          return <FieldRenderer {...field} control={control} />;
        })}
      </div>
    );
  });
}

interface ProfileFormProps {
  control: Control;
  getValues: UseFormGetValues<Record<string, any>>;
}

export default function ProfileForm({ control, getValues }: ProfileFormProps) {
  const companyInformationGroups = groupAndSortFieldsByOrder(profileFields.companyInformation);
  const mediaGalleryGroups = groupAndSortFieldsByOrder(profileFields.mediaGallery);
  const missionAndVisionGroups = groupAndSortFieldsByOrder(profileFields.missionAndVision);
  const profileContactDetailsGroups = groupAndSortFieldsByOrder(
    profileFields.profileContactDetails,
  );
  const brandAssetsGroups = groupAndSortFieldsByOrder(profileFields.brandAssets);
  const values = getValues();

  return (
    <>
      <Section heading="BRAND ASSETS">
        {renderFieldGroups(brandAssetsGroups, control, values)}
      </Section>

      <Section heading="COMPANY INFORMATION">
        {renderFieldGroups(companyInformationGroups, control)}
      </Section>

      <Section heading="MEDIA GALLERY">
        {renderFieldGroups(mediaGalleryGroups, control, values)}
      </Section>

      <Section heading="MISSION & VISION">
        {renderFieldGroups(missionAndVisionGroups, control, values)}
      </Section>

      <Section heading="CONTACT INFORMATION">
        {renderFieldGroups(profileContactDetailsGroups, control, values)}
      </Section>
    </>
  );
}
