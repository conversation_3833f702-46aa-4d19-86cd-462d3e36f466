import { css, useTheme } from "@emotion/react";
import { useLocation, useNavigate } from "@remix-run/react";
import {
  AppTheme,
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RDSButton,
  RDSEmptyState,
  RDSModal,
  RDSProgressSteps,
  RDSTypography,
} from "@roshn/ui-kit";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import { useEffect, useMemo, useState } from "react";
import { Form } from "react-hook-form";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import { Section } from "~/components/section/section";
import { createSvg } from "~/components/svgs";
import { ListingNavStates, ListingStatus, statusVariantMap } from "~/constants/project-tag-config";
import { useAppPath } from "~/hooks/use-app-path";
import { useDynamicForm } from "~/hooks/use-dynamic-form";
import { useAddAsset } from "~/services/project/hooks/use-add-asset";
import { useAddCategory } from "~/services/project/hooks/use-add-category";
import { useCategoryReview } from "~/services/project/hooks/use-category-review";
import { useCategoryUpdate } from "~/services/project/hooks/use-category-update";
import { useGetTemplate } from "~/services/project/hooks/use-get-template";
import { AppPaths } from "~/utils/app-paths";
import { formatAsFormData } from "~/utils/format-as-form-data";
import { groupFieldsByParentType } from "~/utils/groupFieldsByParentType";

import { formatSectionHeading, getFormStatusMessage } from "../common/form-helper";
import { RoshnContainerLoader } from "../common/loading/roshn-container-loader";

export const Check = createSvg(() => import("~/assets/icons/check.svg"));

type StepState = "active" | "complete" | "in-complete";

type StepId = "DRAFT" | "PENDING_REVIEW" | "APPROVED";

type Step = {
  stepTitle: string;
  state: StepState;
  id: StepId;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: (theme: AppTheme) =>
    css({
      flex: "0 0 30%",
      position: "sticky",
      top: theme.rds.dimension["400"],
      alignSelf: "flex-start",
    }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
      height: "fit-content",
    }),

  modalDimension: css({
    // "& div": {
    //   "& div": {
    //     maxWidth: "640px",
    //   },
    // },
    // "& p": {
    //   margin: 0,
    // },
  }),

  radio: (theme: AppTheme) =>
    css({
      "& div": {
        color: theme.rds.color.text.ui.primary,
        position: "relative",
      },
      "& .roshn-boilerplate-fe-18twejl::after": {
        fontSize: "1.1rem",
        content: '" *"',
        color: theme?.rds?.color.text.functional.danger.tertiary,
      },
    }),

  sectionHeadingWrapper: css({
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  }),

  dangerBtn: (theme: AppTheme) =>
    css({
      backgroundColor: theme?.rds?.color.background.functional.danger.darker.default,
      borderColor: theme?.rds?.color.border.functional.danger.darker.default,
      color: theme?.rds?.color.text.brand.primary.onPrimary,
      cursor: "pointer",
      "&:hover": {
        backgroundColor: theme?.rds?.color.background.functional.danger.hover,
        color: theme?.rds?.color.text.functional.danger.onDanger,
        transition: "300ms ease-in-out",
      },
      "&:active": {
        backgroundColor: theme?.rds?.color.background.functional.danger.pressed,
        color: theme?.rds?.color.text.functional.danger.onDanger,
        transition: "300ms ease-in-out",
      },
      "&[data-disabled=true]": {
        cursor: "not-allowed",
        outline: `1px solid ${theme?.rds?.color.border.ui.disabled}`,
        color: theme?.rds?.color.icon.ui.onDisabled,
        backgroundColor: theme?.rds?.color.background.ui.disabled,
      },
    }),

  tableWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  header: (theme: AppTheme) =>
    css({
      paddingInline: "16px",
      paddingBlock: "8px",
      height: theme?.rds?.dimension["500"],
      alignItems: "center",
      display: "flex",
      backgroundColor: theme.rds.color.background.ui.tertiary.default,

      "& > span": {
        ...theme?.rds?.typographies?.label.emphasis.md,
        color: theme.rds.color.text.ui.primary,
        fontWeight: 500,
        width: "50%",
        textAlign: "left",
      },
    }),

  row: (theme: AppTheme, isLast: boolean) =>
    css({
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingInline: "16px",
      paddingBlock: "8px",
      borderBottom: isLast ? "none" : "1px solid #eee",
      backgroundColor: "#fff",
      height: "56px",

      "& > div": {
        width: "50%",
        textAlign: "left",
      },
    }),

  inputWrapper: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      display: "flex",
      alignItems: "center",
    }),

  input: (theme: AppTheme) =>
    css({
      width: "100%",
      padding: theme.rds.dimension["200"],
      textAlign: "right",
    }),

  bottomInpWrapper: css({
    width: "50%",
    alignSelf: "end",
  }),
};

export default function ProjectForm({
  defaultValues = null,
  edit = false,
  editCategoryId,
  metaData = {
    status: "DRAFT",
  },
}: {
  defaultValues?: any;
  edit?: boolean;
  editCategoryId?: number | string;
  metaData?: {
    createdOn: string;
    updatedOn: string;
    createdBy: string;
    updatedBy: string;
    status: string;
  };
}) {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const theme = useTheme() as AppTheme;
  const location = useLocation();

  const isEditBlocked = location.state === ListingNavStates.BLOCK_EDIT;
  const [categoryId, setCategoryId] = useState(editCategoryId);
  const [showReviewModal, setReviewModal] = useState(false);
  const [showSubmitModal, setSubmitModal] = useState(false);
  const [showWarningModal, setWarningModal] = useState(false);
  const [initialValues, setInitialValues] = useState({});

  const { data } = useGetTemplate({
    id: 1,
  });

  const fields = useMemo(() => {
    const sections = data?.category_attributes ?? [];
    return sections.flatMap((section: any) => section.fields || []);
  }, [data]);

  const {
    control,
    formState: { isValid, errors, touchedFields, isDirty },
    reset,
    setValue,
    watch,
    getValues,
    trigger,
    setError,
  } = useDynamicForm(fields, defaultValues);

  const hasTouched = !isEmpty(touchedFields);

  const addAsset = useAddAsset();

  useEffect(() => {
    if (!isEmpty(defaultValues)) {
      setInitialValues(defaultValues);
      trigger();
    }
  }, [defaultValues]);

  useEffect(() => {
    if (metaData?.status) {
      setSteps(setActiveStepById(steps, metaData?.status));
    }
  }, [metaData?.status]);

  const { mutateAsync, isPending: addCatePending } = useAddCategory();
  const { mutateAsync: updateMutate, isPending: updatePending } = useCategoryUpdate({});
  const { mutateAsync: reviewMutate, isPending: reviewPending } = useCategoryReview({});

  const updatedSteps = (prevSteps: Step[]): Step[] => {
    const nextSteps = prevSteps.map((step) => ({ ...step }));

    const activeIndex = nextSteps.findIndex((step) => step.state === "active");
    if (activeIndex !== -1) {
      nextSteps[activeIndex].state = "complete";
    }

    const nextIncompleteIndex = nextSteps.findIndex((step) => step.state === "in-complete");
    if (nextIncompleteIndex !== -1) {
      nextSteps[nextIncompleteIndex].state = "active";
    }

    return nextSteps;
  };

  const setActiveStepById = (steps: Step[], targetId: string): Step[] => {
    return steps.map((step, index) => {
      const targetIndex = steps.findIndex((s) => s.id === targetId);

      if (index < targetIndex) return { ...step, state: "complete" };
      if (index === targetIndex) return { ...step, state: "active" };
      return { ...step, state: "in-complete" };
    });
  };

  const findCurrentStatus = () => {
    return steps.find((i) => i.id === metaData?.status)?.id;
  };

  const [steps, setSteps] = useState<Step[]>([
    { stepTitle: "Draft", state: "active", id: "DRAFT" },
    { stepTitle: "In review", state: "in-complete", id: "PENDING_REVIEW" },
    { stepTitle: "Approved", state: "in-complete", id: "APPROVED" },
  ]);

  if (!fields) {
    return <RoshnContainerLoader />;
  }

  const handleProjectNav = () => {
    navigate(generateAppPath(AppPaths.project));
  };

  const handlePageTransition = () => {
    if (isEditBlocked) {
      return handleProjectNav();
    }
    return hasTouched ? setWarningModal(true) : handleProjectNav();
  };

  const handleProjectDetailNav = () => {
    navigate(generateAppPath(AppPaths.projectDetail, { id: categoryId }));
  };

  const onDiscard = () => {
    const resetValues = {
      ...fields.reduce(
        (acc, field) => {
          acc[field.name] = "";
          return acc;
        },
        {} as Record<string, any>,
      ),
    };

    reset(resetValues);
  };

  const fieldsError = !isEmpty(errors);

  const { project_documents: _doc1, ...restDef } = initialValues ?? {};
  const { project_documents: _doc2, ...restGet } = getValues() ?? {};

  const isValEqual = isEqual(restDef ?? {}, restGet ?? {}) && !touchedFields["project_documents"];

  return (
    <>
      <div css={styles.modalDimension}>
        <RDSModal
          isOpen={showReviewModal}
          showContent
          content={
            <RDSEmptyState
              heading="Ready to submit?"
              appearance="info"
              size="sm"
              tag="div"
              showMedia={false}
              body="Once submitted, you won’t be able to edit your project while our team reviews it for approval. We’ll notify you within 2 business days about the outcome."
              buttons={[
                {
                  text: "SUBMIT FOR REVIEW",
                  variant: "primary",
                  loading: reviewPending,
                  onClick: () => {
                    const currentActiveIdx = steps.findIndex((i) => i.state === "active");
                    const status = steps[currentActiveIdx + 1].id;

                    reviewMutate({
                      categoryId: categoryId as number,
                      payload: {
                        status,
                      },
                    }).then((res) => {
                      if (!res?._innerError) {
                        setReviewModal(false);
                        setSubmitModal(true);
                        setSteps((prev) => updatedSteps(prev));
                      }
                    });
                  },
                },
                {
                  text: "KEEP EDITING",
                  variant: "secondary",
                  onClick: () => setReviewModal(false),
                },
              ]}
            />
          }
        />
        <RDSModal
          isOpen={showSubmitModal}
          showContent
          content={
            <RDSEmptyState
              heading="Project submitted!"
              appearance="success"
              size="sm"
              tag="div"
              body="Your project is now under review. We’ll notify you as soon as there’s an update. In the meantime, you can check your project details or return to all projects."
              buttons={[
                {
                  text: "VIEW PROJECT DETAILS",
                  variant: "primary",
                  onClick: () => {
                    handleProjectDetailNav();
                    setSubmitModal(false);
                  },
                },
                {
                  text: "BACK TO PROJECTS",
                  variant: "secondary",
                  onClick: () => {
                    handleProjectNav();
                    setSubmitModal(false);
                  },
                },
              ]}
            />
          }
        />
        <RDSModal
          isOpen={showWarningModal}
          showContent
          content={
            <RDSEmptyState
              heading="Leave without saving?"
              size="sm"
              body="You have unsaved changes. If you leave now, your updates will be lost. Are you sure you want to exit?"
              buttons={[
                {
                  css: styles.dangerBtn,
                  text: "DISCARD CHANGES AND EXIT",
                  onClick: () => {
                    handleProjectNav();
                  },
                },
                {
                  text: "STAY AND KEEP EDITING",
                  variant: "secondary",
                  onClick: () => {
                    setWarningModal(false);
                  },
                },
              ]}
              showMedia={false}
            />
          }
        />
      </div>
      <div css={styles.wrapper}>
        <RDSButton
          css={styles.button}
          variant="tertiary"
          size="lg"
          text="Back to projects"
          leadIcon="left_arrow"
          onClick={handlePageTransition}
        />
        <div css={styles.sectionsWrapper}>
          <Form css={[styles.form, styles.sectionLayout]} control={control}>
            {groupFieldsByParentType(data?.category_attributes ?? [])?.map((section: any) => (
              <Section
                key={section.section}
                heading={formatSectionHeading(section.section)}
                isGridStyled
              >
                {section.fields.map((field: any) => (
                  <FieldRenderer
                    key={field.name}
                    control={control}
                    setValue={setValue}
                    watch={watch}
                    setError={setError}
                    {...field}
                  />
                ))}
              </Section>
            ))}
          </Form>
          <div css={[styles.sectionLayout, styles.infoSections]}>
            <Section heading="Actions">
              <div css={styles.internalWrapper}>
                <RDSTypography
                  css={[
                    styles.actionsHeadingText,
                    fieldsError && { color: theme.rds.color.text.functional.danger.tertiary },
                  ]}
                >
                  {getFormStatusMessage(errors, touchedFields)}
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  loading={addCatePending || updatePending}
                  onClick={() => {
                    const values = getValues();

                    const addCategoryFields = fields
                      .filter(
                        (field) =>
                          field.attribute_type !== "UPLOAD_FILE" &&
                          field.attribute_type !== "IMAGE" &&
                          field.attribute_type !== "UPLOAD",
                      )
                      .reduce(
                        (acc, field) => {
                          const key = field.custom_attribute_field_key;
                          acc[key] = values[key];
                          return acc;
                        },
                        {} as Record<string, any>,
                      );

                    const imageAsset = fields?.find(
                      (field) => field?.attribute_type === "UPLOAD_FILE",
                    );

                    const fileAssets = fields?.find((field) => field?.attribute_type === "UPLOAD");

                    const assetsFormData = [
                      imageAsset && formatAsFormData(values[imageAsset.name]?.[0], imageAsset),
                      ...(values[fileAssets.name] ? values[fileAssets.name] : []),
                    ];

                    if (edit) {
                      assetsFormData.map((i) =>
                        addAsset.mutate({
                          id: categoryId as number,
                          payload: i,
                        }),
                      );
                      updateMutate({
                        categoryId: categoryId as number,
                        payload: {
                          title: addCategoryFields["name_en"],
                          template_id: data.id,
                          custom_attributes: addCategoryFields,
                          order: data.order,
                        },
                      }).then((res) => {
                        setInitialValues(addCategoryFields);
                        reset(
                          {
                            ...addCategoryFields,
                            logo: values["logo"],
                            project_documents: values["project_documents"],
                          },
                          { keepDirty: true, keepIsValid: true },
                        );
                      });
                    } else {
                      mutateAsync({
                        payload: {
                          title: addCategoryFields["name_en"],
                          template_id: data.id,
                          custom_attributes: addCategoryFields,
                          order: data.order,
                        },
                      }).then((res) => {
                        setInitialValues(addCategoryFields);
                        reset(
                          {
                            ...addCategoryFields,
                            logo: values["logo"],
                            project_documents: values["project_documents"],
                          },
                          { keepDirty: true, keepIsValid: true },
                        );
                        setCategoryId(res.id);
                        assetsFormData.map((i) =>
                          addAsset.mutate({
                            id: res.id,
                            payload: i,
                          }),
                        );
                      });
                    }
                  }}
                  size="md"
                  text="Save Changes"
                  disabled={!isValid || !hasTouched || isValEqual || isEditBlocked}
                />
                <RDSButton
                  variant="secondary"
                  size="md"
                  onClick={() => onDiscard()}
                  text="Discard Changes"
                  disabled={isEditBlocked}
                />
              </div>
            </Section>

            <Section
              heading="LISTING STATUS"
              tag={{
                label: findCurrentStatus(),
                appearance: statusVariantMap[findCurrentStatus()].variant,
              }}
            >
              <div css={styles.internalWrapper}>
                <RDSProgressSteps type="number" steps={steps} size="md" />
                <RDSTypography css={styles.actionsHeadingText}>
                  Your project is not visible to customers. Please complete all required fields
                  before submitting for review.
                </RDSTypography>
                <RDSButton
                  variant="primary"
                  size="md"
                  text="SUBMIT FOR REVIEW"
                  leadIcon={
                    <RDSAssetWrapper>
                      <Check />
                    </RDSAssetWrapper>
                  }
                  loading={reviewPending}
                  disabled={
                    !isValid ||
                    addCatePending ||
                    updatePending ||
                    isEditBlocked ||
                    metaData?.status === ListingStatus.PENDING_REVIEW
                  }
                  onClick={() => setReviewModal(true)}
                />
              </div>
            </Section>

            <Section heading="Information">
              <div css={styles.actionsLayout}>
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>{metaData?.createdOn ?? "-"}</RDSTypography>
                </div>
                {/* <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div> */}
                <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                  <RDSTypography css={styles.infoDes}>{metaData?.updatedOn ?? "-"}</RDSTypography>
                </div>
                {/* <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div> */}
              </div>
            </Section>
          </div>
        </div>
      </div>
    </>
  );
}
