import { css, useTheme } from "@emotion/react";
import { useNavigate, useParams } from "@remix-run/react";
import {
  AppTheme,
  RDSButton,
  RDSTypography,
  RDSSwitch,
  RDSTextInput,
  RDSModal,
} from "@roshn/ui-kit";
import isEmpty from "lodash/isEmpty";
import { useEffect, useState } from "react";
import { Form } from "react-hook-form";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import { Section } from "~/components/section/section";
import { formatSectionHeading, getFormStatusMessage } from "~/features/common/form-helper";
import { useAppPath } from "~/hooks/use-app-path";
import { useDeleteUnit } from "~/hooks/use-delete-unit";
import { useInjection } from "~/hooks/use-di";
import { useDynamicForm } from "~/hooks/use-dynamic-form";
import { useAddUnit } from "~/services/product/hooks/use-add-unit";
import { useAddUnitAssets } from "~/services/product/hooks/use-add-unit-assets";
import { useEditUnit } from "~/services/product/hooks/use-edit-unit";
import { StoreService } from "~/services/store-service/store-service-impl";
import { AppPaths } from "~/utils/app-paths";
import { toDate } from "~/utils/to-date";

import { PriceTable } from "./price-table";
import { useStatusUpdate } from "~/services/product/hooks/use-status-update";

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      width: "30vw",
      overflow: "auto",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  hideShowUnitWrapper: () =>
    css({
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
    }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoSections: (theme: AppTheme) =>
    css({
      flex: "0 0 30%",
      position: "sticky",
      top: theme.rds.dimension["400"],
      alignSelf: "flex-start",
    }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  modalDimension: css({
    "& div": {
      "& div": {
        maxWidth: "640px",
      },
    },
    "& p": {
      margin: 0,
    },
  }),
};

export default function ProductForm({
  updatedDefaultValues = undefined,
  productData = {},
  productFields = {},
  schema = {},
}: {
  updatedDefaultValues?: any;
  productData?: any;
  productFields?: any;
  schema?: any;
}) {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const theme = useTheme() as AppTheme;
  const { id, unitId = null } = useParams();
  const storeService = useInjection(StoreService);
  const [showDeletion, setDeletion] = useState(false);
  const [delModalInput, setDelModalInp] = useState("");

  const {
    control,
    formState: { isValid, errors, touchedFields, isDirty },
    reset,
    setValue,
    watch,
    getValues,
    trigger,
    setError,
  } = useDynamicForm(productFields, updatedDefaultValues);

  useEffect(() => {
    if (!isEmpty(updatedDefaultValues)) {
      trigger();
    }
  }, [updatedDefaultValues]);

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.projectDetail, { id }));
  };

  const handleDiscard = () => {
    navigate(generateAppPath(AppPaths.projectDetail, { id }));
  };

  const deleteUnit = useDeleteUnit({
    onSuccess: () => {
      navigate(generateAppPath(AppPaths.projectDetail, { id }));
    },
  });

  const handleProjectDeletion = () => {
    deleteUnit.mutateAsync({ unitId });
  };

  const categoryDetails = storeService.state.categoryDetails;
  const fieldsError = !isEmpty(errors);

  const { mutateAsync, isPending: addUnitPending } = useAddUnit();
  const { mutateAsync: updateMutate, isPending: updatePending } = useEditUnit({});
  const { mutateAsync: addUnitAssets, isPending: addUnitAssetsPending } = useAddUnitAssets({});
  const { mutateAsync: updateStatus, isPending: statusUpdatePending } = useStatusUpdate({});

  const columnsData = (price: number) => [
    {
      label: "Reservation fee",
      number: categoryDetails?.custom_attributes?.reservation_fee_ammount,
      type: categoryDetails?.custom_attributes?.reservation_fee_type,
    },
    {
      label: "Down payment",
      number: price ? price * (categoryDetails?.custom_attributes?.down_payment / 100) : "-",
      type: `${categoryDetails?.custom_attributes?.down_payment} % of total`,
    },
    {
      label: "During construction",
      number: price ? price * (categoryDetails?.custom_attributes?.during_construction / 100) : "-",
      type: `${categoryDetails?.custom_attributes?.during_construction} % of total`,
    },
    {
      label: "On handover",
      number: price ? price * (categoryDetails?.custom_attributes?.on_handover / 100) : "-",
      type: `${categoryDetails?.custom_attributes?.on_handover} % of total`,
    },
  ];

  const columnHeading = ["Payment type", "Amount to pay", "% per installment"];

  const uploadAllImages = async () => {
    const files = getValues()["image_gallery"];
    const filteredFilesArray = files.filter((file: any) => file instanceof File);
    const uploadedAssetIds = files.map((file: any) => {
      if (!(file instanceof File) && file.id) {
        return file.id;
      }
    });
    const uploadedImages = await Promise.all(
      filteredFilesArray.map((file) =>
        addUnitAssets({
          payload: { file },
        }),
      ),
    );
    return [
      ...uploadedAssetIds.filter((item) => item != null),
      ...uploadedImages.filter((item) => item != null),
    ];
  };

  const uploadAssetAndSubmit = async (addUnitFields: { label: string; value: string }[]) => {
    const ids = await uploadAllImages();
    if (unitId) {
      updateMutate(
        {
          unitId,
          payload: {
            title: categoryDetails?.title,
            template_id: schema.id,
            custom_attributes: addUnitFields,
            price: getValues()["pricing"],
            images: ids,
            marketplace_categories: [categoryDetails?.marketplace_category.id],
            marketplace_merchant_category: id,
          },
        },
        {
          onError: (err) => {
            setError("button", {
              type: "failedUnitAdd",
              message: "Failed to update unit",
            });
          },
          onSuccess: (res) => {
            navigate(generateAppPath(AppPaths.projectDetail, { id }));
          },
        },
      );
    } else {
      mutateAsync(
        {
          payload: {
            title: categoryDetails?.title,
            template_id: schema.id,
            price: getValues()["pricing"],
            images: ids,
            custom_attributes: addUnitFields,
            marketplace_categories: [categoryDetails?.marketplace_category.id],
            marketplace_merchant_category: id,
          },
        },
        {
          onError: (err) => {
            setError("button", {
              type: "failedUnitAdd",
              message: "Failed to create unit",
            });
          },
          onSuccess: (res) => {
            navigate(generateAppPath(AppPaths.projectDetail, { id }));
          },
        },
      );
    }
  };

  const handleStatusChange = () => {
    if (unitId) {
      updateStatus({
        unitId: unitId as string,
        status: productData?.status === "ACTIVE" ? "HIDDEN" : "ACTIVE",
      }, {
        onSuccess: (res) => {
          navigate(generateAppPath(AppPaths.projectDetail, { id }));
        },
      });
    }
  };

  return (
    <div css={styles.wrapper}>
      <div css={styles.modalDimension}>
        <RDSModal
          headerProps={{
            label: "Confirm unit deletion",
            type: "centred",
          }}
          isOpen={showDeletion}
          buttonsGroup={{
            buttons: [
              <RDSButton
                variant="primary"
                onClick={handleProjectDeletion}
                text="DELETE UNIT"
                key="delete"
                disabled={delModalInput !== "DELETE"}
              />,
              <RDSButton
                variant="secondary"
                onClick={() => setDeletion(false)}
                text="CANCEL"
                key="cancel"
              />,
            ],
            direction: "vertical",
          }}
          content={
            <RDSTextInput
              value={delModalInput}
              onChange={(e) => setDelModalInp(e.target.value)}
              placeholder="DELETE"
            />
          }
          description={
            (
              <>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  Deleting this unit is permanent. We recommend saving it as a draft if you may need
                  it later.
                </RDSTypography>
                <RDSTypography fontName={theme?.rds?.typographies.body.md}>
                  To confirm deletion, please type DELETE in the box below.
                </RDSTypography>
              </>
            ) as any
          }
          showContent
          showDescription
        />
      </div>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to details page"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <Form css={[styles.form, styles.sectionLayout]} control={control}>
          {schema?.product_attributes?.map((section: any) => (
            <Section
              key={section.section}
              heading={formatSectionHeading(section.section)}
              isGridStyled
            >
              {section.fields.map((field: any) => (
                <>
                  <FieldRenderer
                    key={field.name}
                    control={control}
                    setValue={setValue}
                    watch={watch}
                    {...field}
                  />
                  {section.section === "price" && (
                    <PriceTable
                      label="Pricing"
                      child={columnsData(watch(field.custom_attribute_field_key))}
                      control={control}
                      columns={columnHeading}
                    />
                  )}
                </>
              ))}
            </Section>
          ))}
        </Form>
        <div css={[styles.sectionLayout, styles.infoSections]}>
          <Section heading="Actions">
            <div css={styles.internalWrapper}>
              <RDSTypography
                css={[
                  styles.actionsHeadingText,
                  fieldsError && { color: theme.rds.color.text.functional.danger.tertiary },
                ]}
              >
                {getFormStatusMessage(errors, touchedFields)}
              </RDSTypography>
              <RDSButton
                variant="primary"
                loading={addUnitPending || updatePending || addUnitAssetsPending || statusUpdatePending}
                onClick={() => {
                  const addUnitFields = productFields
                    .filter(
                      (field) =>
                        field.attribute_type !== "UPLOAD_FILE" ||
                        field.attribute_type !== "IMAGE" ||
                        field.attribute_type !== "UPLOAD" ||
                        field.attribute_type !== "GALLERY",
                    )
                    .reduce(
                      (acc, field) => {
                        const key = field.custom_attribute_field_key;
                        acc.push({ label: key, value: getValues()[key] });
                        return acc;
                      },
                      [] as Record<string, any>[],
                    );
                  uploadAssetAndSubmit(addUnitFields);
                }}
                size="md"
                text="Save Changes"
                disabled={!isValid || !isDirty}
              />
              <RDSButton
                variant="secondary"
                size="md"
                onClick={handleDiscard}
                text="Discard Changes"
              />
            </div>
          </Section>
          <Section
            heading="UNIT VISIBILITY"
            tag={{
              label: productData?.status === "ACTIVE" ? "Visible" : "Hidden",
              appearance: productData?.status === "ACTIVE" ? "success" : "neutral",
            }}
          >
            <div css={styles.hideShowUnitWrapper}>
              <RDSTypography fontName={theme?.rds?.typographies?.label?.md}>
                Show to Customers
                <RDSTypography fontName={theme?.rds?.typographies?.label?.sm}>
                  Saved changes update the live unit instantly.
                </RDSTypography>
              </RDSTypography>
              <RDSSwitch
                checked={productData?.status === "ACTIVE"}
                disabled={!isValid}
                onCheckedChange={handleStatusChange}
              />
            </div>
          </Section>
          <Section heading="Information">
            <div css={styles.actionsLayout}>
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Created on</RDSTypography>
                <RDSTypography css={styles.infoDes}>
                  {toDate(productData?.created_date) ?? "-"}
                </RDSTypography>
              </div>
              {/* <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Created by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div> */}
              <div css={styles.infoTypoWrapper}>
                <RDSTypography css={styles.infoHead}>Updated on</RDSTypography>
                <RDSTypography css={styles.infoDes}>
                  {toDate(productData?.updated_date) ?? "-"}
                </RDSTypography>
              </div>
              {/* <div css={styles.infoTypoWrapper}>
                  <RDSTypography css={styles.infoHead}>Updated by</RDSTypography>
                  <RDSTypography css={styles.infoDes}>-</RDSTypography>
                </div> */}
            </div>
          </Section>
          <Section heading="DELETE UNIT">
            <div css={styles.internalWrapper}>
              <RDSTypography css={styles.actionsHeadingText}>
                Deleting this unit is permanent. Consider hiding it from public visibility instead.
              </RDSTypography>
              <RDSButton
                variant="secondary"
                size="lg"
                text="DELETE UNIT"
                disabled={unitId === null}
                onClick={() => setDeletion(true)}
              />
            </div>
          </Section>
        </div>
      </div>
    </div>
  );
}
