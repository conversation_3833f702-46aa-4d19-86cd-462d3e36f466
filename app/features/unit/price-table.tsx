import { css, useTheme } from "@emotion/react";
import { AppTheme, RDSTypography } from "@roshn/ui-kit";

// import LabelWrapper from "~/components/ui/label-wrapper/label-wrapper";
import { useMoneyFormatter } from "~/hooks/use-money-formatter";

const styles = {
  primaryWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      width: "100%",
    }),
  tableWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  header: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["200"],
      paddingBlock: theme.rds.dimension["100"],
      height: theme?.rds?.dimension["500"],
      alignItems: "center",
      display: "flex",
      backgroundColor: theme.rds.color.background.ui.tertiary.default,
    }),

  headerTypo: (theme: AppTheme) =>
    css({
      // ...theme?.rds?.typographies?.label.md,
      color: theme.rds.color.text.ui.primary,
      width: "50%",
      textAlign: "left",
    }),

  columnData: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.md,
      color: theme.rds.color.text.ui.tertiary,
      width: "50%",
      textAlign: "left",
    }),

  inputWrapper: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      display: "flex",
      alignItems: "center",
    }),

  row: (theme: AppTheme, isLast: boolean) =>
    css({
      color: theme.rds.color.text.ui.primary,
      // fontWeight: 500,
      display: "flex",
      alignItems: "center",
      paddingInline: "16px",
      paddingBlock: "8px",
      borderBottom: isLast ? "none" : "1px solid #eee",
      backgroundColor: "#fff",
      height: "fit-content",

      "& > div": {
        width: "33%",
        textAlign: "left",
      },
    }),

  bottomInpWrapper: css({
    width: "50%",
    alignSelf: "end",
  }),
};

export function PriceTable({
  label,
  child = [],
  control,
  columns,
  ...rest
}: {
  control: any;
  label: string;
  columns: string[];
  child: { label: string; number: number; type: string }[];
}) {
  const theme = useTheme();
  const moneyFormat = useMoneyFormatter();
  const formatOptions = {
    numberOfDigits: 0,
    usingCode: false,
  };

  const moneyData = (amount: number) =>
    moneyFormat(amount, formatOptions) === "NaN" ? "--" : moneyFormat(amount, formatOptions);

  return (
    <div css={styles.primaryWrapper}>
      <div css={styles.tableWrapper}>
        <div css={styles.header}>
          {columns.map((i) => (
            <span css={styles.headerTypo}>{i}</span>
          ))}
        </div>
        {child.map(({ label, number, type }, idx) => (
          <div key={label} css={styles.row(theme, idx === child.length - 1)}>
            {label && (
              <RDSTypography
                fontName={theme?.rds?.typographies?.body.emphasis.md}
                css={styles.headerTypo}
              >
                {label}
              </RDSTypography>
            )}
            <RDSTypography css={styles.columnData}>{moneyData(number)}</RDSTypography>
            <RDSTypography css={styles.columnData}>{type}</RDSTypography>
          </div>
        ))}
      </div>
    </div>
  );
}
