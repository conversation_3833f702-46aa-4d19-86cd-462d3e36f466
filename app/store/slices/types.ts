import { SignedInState } from "~/services/auth/implementations/forge-rock/fr-auth-impl";

export interface AuthSlice {
  signedIn: boolean;
  setSignedIn: (val: SignedInState) => void;
}

export interface ProfileSlice {
  profile?: any;
  setProfile: (p: any) => void;
}

export interface MerchantDataSlice {
  merchantData?: any;
  categoryDetails?: any;
  setMerchantData: (data: any) => void;
  setCategoryDetails: (data: any) => void;
}
