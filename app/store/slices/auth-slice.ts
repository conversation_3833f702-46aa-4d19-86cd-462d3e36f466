import type { StateCreator } from "zustand";

import { SignedInState } from "~/services/auth/implementations/forge-rock/fr-auth-impl";

import type { AuthSlice } from "./types";

export const createAuthSlice: StateCreator<AuthSlice> = (set) => ({
  signedIn: false,
  setSignedIn: (val: SignedInState | false) => {
    if (typeof val === 'boolean') {
      set({ signedIn: val });
    } else {
      set(val);
    }
  },
});
