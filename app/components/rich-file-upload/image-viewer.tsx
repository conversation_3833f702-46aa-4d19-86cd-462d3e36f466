import {
  useFileUrl,
  useMultipleFileUrls,
  type FileSource,
  type FileData,
} from "~/hooks/use-file-url";

import { createSvg } from "../svgs";

import { ImageViewerCarouselItem, ImageViewerCarousel } from "./image-viewer-carousel";

const Edit = createSvg(
  () => import("~/assets/icons/pencil-box.svg") as unknown as Promise<{ ReactComponent: any }>,
);

const Trash = createSvg(
  () => import("~/assets/icons/image-trash.svg") as unknown as Promise<{ ReactComponent: any }>,
);

// Image-specific viewer
export const ImageViewer = ({
  source,
  name,
  onEdit,
  onDelete,
}: {
  source: FileSource;
  name?: string;
  className?: string;
  style?: React.CSSProperties;
  onEdit: () => void;
  onDelete: () => void;
}) => {
  const { objectUrl, fileName, fileType, fileSize } = useFileUrl({ source }) || {};
  const displayName = name || fileName;
  if (!objectUrl) return null;

  return (
    <ImageViewerCarouselItem
      images={[objectUrl]}
      actions={[
        {
          icon: (
            <Edit
              css={{
                "& path": {
                  stroke: "none",
                },
              }}
            />
          ),
          onClick: onEdit,
        },
        {
          icon: (
            <Trash
              css={{
                "& path": {
                  stroke: "none",
                },
              }}
            />
          ),
          onClick: onDelete,
        },
      ]}
      footerText={displayName}
      footerSubText={fileSize ?? ""}
      style={{
        width: "200px",
      }}
    />
  );
};

// Multiple images viewer
export const MultipleImageViewer = ({
  sources,
  transformData,
}: {
  sources: FileSource[];
  transformData?: (item: FileData[]) => React.ComponentProps<typeof ImageViewerCarousel>["data"];
}) => {
  const fileData = useMultipleFileUrls({ sources });
  const data = transformData
    ? transformData(fileData)
    : fileData.map((item) => ({
        images: [item?.objectUrl],
        actions: [],
        tags: [],
      }));
  return <ImageViewerCarousel data={data} />;
};
