import { css, useTheme } from "@emotion/react";
import { AppTheme, RDSHelperText, RDSTypography as Typography } from "@roshn/ui-kit";
import { ArrowUpCloud } from "@roshn/ui-kit/assets/icons/index.js";
import React, { useCallback, useImperativeHandle, forwardRef } from "react";

const styles = {
  hiddenContainer: css({
    visibility: "hidden",
    height: 0,
  }),
  container: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "flex-start",
      flexDirection: "column",
      gap: theme?.rds?.dimension[200],
    }),
  inputContainer: css({
    position: "relative",
    width: "100%",
  }),
  input: css({
    '&[type="file"]': {
      opacity: 0,
      position: "absolute",
      width: "100%",
    },
  }),
  placeholder: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.brand.primary.default,
      ...theme?.rds?.typographies.label.md,
      "[data-disabled=true] &": {
        color: theme?.rds?.color.text.ui.onDisabled,
      },
    }),
  visibleContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      alignSelf: "stretch",
      gap: theme?.rds?.dimension[100],
      padding: theme?.rds?.dimension[200],
      background: theme?.rds?.color.background.ui.secondary.default,
      borderWidth: theme?.rds?.border.borderWidth[200],
      borderColor: theme?.rds?.color.border.ui.secondary,
      width: "100%",
      borderStyle: "dashed",
      cursor: "pointer",
      "[data-disabled=true] &": {
        background: theme?.rds?.color.background.ui.disabled,
        borderColor: theme?.rds?.color.border.ui.disabled,
        cursor: "not-allowed",
      },
      direction: "ltr",
    }),
  labelContainer: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "flex-start",
      alignSelf: "stretch",
      gap: theme?.rds?.dimension[100],
      flexDirection: "column",
    }),
  label: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies.label.md,
      color: theme?.rds?.color.text.ui.primary,
      position: "relative",
      paddingInlineEnd: theme?.rds?.dimension[25],
    }),
  required: (theme: AppTheme) => {
    const isRTL = theme?.rds?.direction === "rtl";
    const left = isRTL ? "right" : "left";
    return css({
      "&::after": {
        position: "absolute",
        [left]: "100%",
        content: '"*"',
        color: theme?.rds?.color.text.functional.danger.tertiary,
      },
    });
  },
  caption: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies.label.sm,
      marginBlockEnd: "16px",
      color: theme?.rds?.color.text.ui.tertiary,
      "[data-disabled=true] &": {
        color: theme?.rds?.color.text.ui.disabled,
      },
    }),
};

// Default file handling utilities
export const createFileSelectHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
  options?: {
    mode?: "replace" | "append";
    maxFiles?: number;
    allowedTypes?: string[];
    maxSize?: number; // in bytes
  },
) => {
  const { mode = "append", maxFiles, allowedTypes, maxSize } = options || {};

  return (newFiles: File[]) => {
    let validFiles = newFiles;

    // Filter by file type
    if (allowedTypes) {
      validFiles = validFiles.filter((file) =>
        allowedTypes.some((type) => file.type.includes(type)),
      );
    }

    // Filter by file size
    if (maxSize) {
      validFiles = validFiles.filter((file) => file.size <= maxSize);
    }

    if (mode === "replace") {
      setFiles(validFiles.slice(0, maxFiles));
    } else {
      // Append mode with deduplication
      const existingNames = new Set(currentFiles.map((f) => f.name));
      const uniqueFiles = validFiles.filter((f) => !existingNames.has(f.name));
      const combined = [...currentFiles, ...uniqueFiles];
      setFiles(maxFiles ? combined.slice(0, maxFiles) : combined);
    }
  };
};

export const createFileDropHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
  options?: Parameters<typeof createFileSelectHandler>[2],
) => createFileSelectHandler(currentFiles, setFiles, options);

// Delete file utilities
export const createFileDeleteHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
) => {
  return (fileToDelete: File | string) => {
    const fileName = typeof fileToDelete === "string" ? fileToDelete : fileToDelete.name;
    const updatedFiles = currentFiles.filter((file) => file.name !== fileName);
    setFiles(updatedFiles);
  };
};

// Delete by index (useful for file lists)
export const createFileDeleteByIndexHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
) => {
  return (index: number) => {
    if (index >= 0 && index < currentFiles.length) {
      const updatedFiles = currentFiles.filter((_, i) => i !== index);
      setFiles(updatedFiles);
    }
  };
};

// Clear all files
export const createFileClearHandler = (setFiles: (files: File[]) => void) => {
  return () => setFiles([]);
};

// Replace file utilities
export const createFileReplaceHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
) => {
  return (oldFile: File | string, newFile: File) => {
    const fileName = typeof oldFile === "string" ? oldFile : oldFile.name;
    const updatedFiles = currentFiles.map((file) => (file.name === fileName ? newFile : file));
    setFiles(updatedFiles);
  };
};

// Replace file by index
export const createFileReplaceByIndexHandler = (
  currentFiles: File[],
  setFiles: (files: File[]) => void,
) => {
  return (index: number, newFile: File) => {
    if (index >= 0 && index < currentFiles.length) {
      const updatedFiles = [...currentFiles];
      updatedFiles[index] = newFile;
      setFiles(updatedFiles);
    }
  };
};

const AcceptTypes = {
  pdf: "application/pdf",
  doc: "application/msword",
  docx: "application/msword",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.ms-excel",
  ppt: "application/vnd.ms-powerpoint",
  pptx: "application/vnd.ms-powerpoint",
  image: "image/*",
  video: "video/*",
  audio: "audio/*",
  application: "application/*",
  text: "text/*",
};

export type UploadFileProps = Omit<React.ComponentProps<"input">, "onFileSelect" | "onFileDrop"> & {
  label?: string;
  caption?: string;
  isRequired?: boolean;
  browseFilePlaceholder?: string;
  validationMsg?: string;
  validationType?: "success" | "error";
  acceptType?: Array<keyof typeof AcceptTypes>;
  onFileSelect?: (files: File[]) => void;
  onFileDrop?: (files: File[]) => void;
  // Non-DOM props that should be filtered out
  isSubmitting?: boolean;
  currentFiles?: File[];
  multiple?: boolean;
  hidden?: boolean;
};

// Interface for imperative handle
export interface FileUploadRef {
  triggerUpload: () => void;
}

export const FileUpload = forwardRef<FileUploadRef, UploadFileProps>(
  (
    {
      onChange,
      browseFilePlaceholder = "Browse or drop a file",
      onFileSelect,
      hidden,
      onFileDrop,
      acceptType,
      multiple = false,
      // Filter out non-DOM props
      isSubmitting: _isSubmitting,
      currentFiles: _currentFiles,
      ...rest
    },
    ref,
  ) => {
    const inputRef = React.useRef<HTMLInputElement>(null);
    const theme = useTheme() as AppTheme;
    const iconColor = rest.disabled ? theme?.rds?.color.icon.ui.disabled : undefined;

    const triggerToUpload = useCallback(() => {
      inputRef.current?.click();
    }, []);

    // Expose triggerUpload function via ref
    useImperativeHandle(
      ref,
      () => ({
        triggerUpload: triggerToUpload,
      }),
      [triggerToUpload],
    );

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files) {
        const files = Array.from(e.target.files);
        onFileSelect?.(files);
        if (inputRef.current) {
          inputRef.current.value = "";
        }
      }
      onChange?.(e);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (e.dataTransfer.files) {
        const files = Array.from(e.dataTransfer.files);
        onFileDrop?.(files);
        if (inputRef.current) {
          inputRef.current.value = "";
        }
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === "Enter" || e.key === " ") {
        e.preventDefault();
        inputRef.current?.click();
      }
    };

    const accept = acceptType?.map((type) => AcceptTypes[type] || type).join(", ");

    return (
      <div css={hidden ? styles.hiddenContainer : styles.container} data-disabled={rest.disabled}>
        <div css={styles.inputContainer} onDragOver={(e) => e.preventDefault()} onDrop={handleDrop}>
          <input
            css={styles.input}
            ref={inputRef}
            type="file"
            multiple={multiple}
            onChange={handleFileChange}
            placeholder={browseFilePlaceholder}
            {...rest}
            accept={accept}
          />
          <div
            css={styles.visibleContainer}
            onClick={() => inputRef.current?.click()}
            onKeyDown={handleKeyDown}
            role="button"
            tabIndex={0}
          >
            <ArrowUpCloud color={iconColor} />
            <Typography fontName={theme?.rds?.typographies.label.md} css={styles.placeholder}>
              {browseFilePlaceholder}
            </Typography>
          </div>
        </div>
      </div>
    );
  },
);

export const FileUploadHeader = ({
  label,
  caption,
  isRequired = true,
}: {
  label: string;
  caption?: string;
  isRequired?: boolean;
}) => {
  const theme = useTheme();
  return (
    (label || caption) && (
      <div css={styles.labelContainer}>
        <Typography
          fontName={theme?.rds?.typographies.label.md}
          css={[styles.label, isRequired && styles.required]}
        >
          {label}
        </Typography>
        <Typography fontName={theme?.rds?.typographies.label.sm} css={styles.caption}>
          {caption}
        </Typography>
      </div>
    )
  );
};

export const FileUploadFooter = ({
  disabled,
  validationMsg,
  validationType,
}: {
  disabled: boolean;
  validationMsg: string;
  validationType: "success" | "error";
}) => {
  return (
    !disabled &&
    validationMsg && (
      <RDSHelperText
        isInvalid={validationType ? validationType === "error" : undefined}
        text={validationMsg}
        icon
      />
    )
  );
};

FileUpload.displayName = "FileUpload";
