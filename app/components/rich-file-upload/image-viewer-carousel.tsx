import { RDS<PERSON>elperText, RDSMediaCarousel, RDSTypography } from "@roshn/ui-kit";
import { Swiper, SwiperSlide } from "swiper/react";

import "swiper/css";
import { useRef } from "react";

export const ImageViewerCarouselItem = ({
  images,
  actions,
  tags,
  footerText,
  footerSubText,
  style,
  ...rest
}: {
  images: string[];
  actions?: { icon: React.ReactNode; onClick: () => void }[];
  tags?: { label: string; type: string }[];
  footerText?: string;
  footerSubText?: string;
  style?: React.CSSProperties;
  className?: string;
}) => {
  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        boxShadow: "0px 8px 12px 0px rgba(0, 0, 0, 0.10)",
        marginBottom: "16px",
        height: "fit-content",
        ...style,
      }}
      {...rest}
    >
      <div>
        <RDSMediaCarousel
          //@ts-expect-error - actions is not defined in the RDSMediaCarousel component
          actions={actions}
          bottomNavigation={false}
          carouselCaret={false}
          aspectRatio="4 / 3"
          images={images}
          tags={tags}
          css={{
            objectFit: "contain",
          }}
        />
      </div>
      {(footerText || footerSubText) && (
        <div style={{ display: "flex", flexDirection: "column", gap: "8px", padding: "16px" }}>
          <RDSTypography
            style={{
              textOverflow: "ellipsis",
              overflow: "hidden",
              whiteSpace: "nowrap",
              width: "100%",
            }}
          >
            {footerText}
          </RDSTypography>
          <RDSHelperText text={footerSubText} />
        </div>
      )}
    </div>
  );
};

export const ImageViewerCarousel = ({
  data,
  ...rest
}: {
  data: React.ComponentProps<typeof ImageViewerCarouselItem>[];
}) => {
  const divRef = useRef<HTMLDivElement>(null);

  const containerWidth = divRef?.current?.getBoundingClientRect()?.width;

  return (
    <div ref={divRef} css={{ marginBlockStart: "16px", maxWidth: containerWidth }}>
      <Swiper direction="horizontal" slidesPerView={4.25} spaceBetween={24} {...rest}>
        {data.map((item, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <SwiperSlide key={`${index}-${item.images[0]}`}>
            <ImageViewerCarouselItem
              images={item.images}
              actions={item.actions}
              tags={item.tags}
              footerSubText={item.footerSubText}
              footerText={item.footerText}
            />
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};
