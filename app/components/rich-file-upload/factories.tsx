import { Delete } from "@roshn/ui-kit/assets/icons/index.js";
import { useEffect } from "react";

import { FileData } from "~/hooks/use-file-url";

import { ImageViewer, MultipleImageViewer } from "./image-viewer";
import { RichFileUpload } from "./rich-file-upload";
import { FileManager, FileManagerOptions, useFileManager } from "./use-file-manager";

type RenderViewer = {
  files: File[];
  allowEditing: boolean;
  fileManager: FileManager;
};

export const buildViewerComponent = ({ type }: { type: "single-image" | "multiple-images" }) => {
  return function ImageViewerComponent({ files, allowEditing, fileManager }: RenderViewer) {
    if (files.length === 0) return null;

    if (type === "single-image") {
      const index = 0;
      return (
        <ImageViewer
          source={files[index]}
          onEdit={() => {
            if (allowEditing) {
              fileManager.startEdit(index);
            }
          }}
          onDelete={() => {
            fileManager.deleteFileAtIndex(index);
          }}
        />
      );
    }

    if (type === "multiple-images") {
      const transformData = (item: FileData[]) => {
        return item.map((item, index) => {
          const actions = [
            {
              icon: (<Delete />) as React.ReactNode,
              onClick: () => {
                fileManager.deleteFileAtIndex(index);
              },
            },
          ];
          let tags = undefined;

          if (index === 0) {
            tags = [
              {
                label: "Cover Photo",
                type: "neutral",
              },
            ];
          }
          return {
            images: [item?.objectUrl as string],
            actions,
            tags,
            footerText: item?.fileName,
            footerSubText: item?.fileSize as string,
          };
        });
      };
      return <MultipleImageViewer sources={files} transformData={transformData} />;
    }

    return null;
  };
};

export const buildFileUploadComponent = ({
  label,
  caption,
  fileOptions,
  allowEditing,
  browseFilePlaceholder,
  required,
  renderViewer,
}: {
  label: string;
  caption?: string;
  fileOptions: FileManagerOptions;
  allowEditing: boolean;
  browseFilePlaceholder: string;
  required: boolean;
  renderViewer?: ({ files, allowEditing, fileManager }: RenderViewer) => React.ReactNode;
}) => {
  return function FileUploadComponent({
    onChange,
    value,
    error,
    validationMsg,
    validationType,
  }: {
    onChange: (files: File[]) => void;
    value: File[];
    error?: any;
    validationMsg?: string;
    validationType?: any;
  }) {
    const multiple = Boolean(fileOptions.maxFiles && fileOptions.maxFiles > 1);
    const fileManager = useFileManager(value || [], {
      maxFiles: 10,
      allowedTypes: [],
      maxSize: 10 * 1024 * 1024,
      mode: "append",
      allowDuplicates: false,
      ...fileOptions,
    });

    // Handle file selection with validation feedback
    const handleFileSelect = (newFiles: File[]) => {
      const validation = fileManager.handleFileSelect(newFiles);
      //   onFileValidation?.(validation);
      if (!validation.isValid) {
        console.error("File validation errors:", validation.errors);
      }
      return validation;
    };

    useEffect(() => {
      onChange?.(fileManager.files);
    }, [fileManager.files]);

    const hideUploadSection = !multiple && fileManager.files.length > 0;
    const shouldRenderFileViewer =
      fileManager.files.length > 0 && renderViewer && typeof renderViewer === "function";
    const errorMessage = fileManager.error || error?.message || validationMsg;

    return (
      <>
        <RichFileUpload.Container>
          <RichFileUpload.FileUploadHeader label={label} isRequired={required} caption={caption} />
          <RichFileUpload.FileUpload
            label={label}
            acceptType={fileOptions.allowedTypes as any}
            hidden={hideUploadSection}
            ref={fileManager.fileUploadRef}
            onFileSelect={handleFileSelect}
            onFileDrop={handleFileSelect}
            multiple={!fileManager.isEditing && multiple}
            browseFilePlaceholder={browseFilePlaceholder}
          />
          <RichFileUpload.FileUploadFooter
            disabled={false}
            validationMsg={errorMessage}
            validationType={validationType}
          />
        </RichFileUpload.Container>
        {shouldRenderFileViewer &&
          renderViewer({
            files: fileManager.files,
            allowEditing,
            fileManager,
          })}
      </>
    );
  };
};
