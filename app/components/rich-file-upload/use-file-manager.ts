import { useState, useRef, useCallback } from "react";

import type { FileUploadRef } from "./file-upload";

export interface FileManagerOptions {
  maxFiles?: number;
  allowedTypes?: string[];
  maxSize?: number; // in bytes
  mode?: "replace" | "append";
  allowDuplicates?: boolean; // New option to allow duplicate file names
  skipValidation?: boolean; // New option to skip validation when handled externally
  validateOnAllOperations?: boolean; // Validate after every operation (add, delete, etc.)
}

export interface FileManagerValidation {
  isValid: boolean;
  errors: string[];
  validFiles: File[];
  rejectedFiles: File[];
}

export type FileManager = ReturnType<typeof useFileManager>;

export const useFileManager = (initialFiles: File[] = [], options: FileManagerOptions = {}) => {
  const [files, setFiles] = useState<File[]>(initialFiles);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileUploadRef = useRef<FileUploadRef>(null);

  const {
    maxFiles = 10,
    allowedTypes = [],
    maxSize = 10 * 1024 * 1024, // 10MB default
    mode = "append",
    allowDuplicates = false,
    skipValidation = false,
    validateOnAllOperations = false,
  } = options;

  // Helper function to format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }, []);

  // Validation function
  const validateFiles = useCallback(
    (newFiles: File[], currentFiles: File[] = files): FileManagerValidation => {
      const errors: string[] = [];
      const validFiles: File[] = [];
      const rejectedFiles: File[] = [];

      newFiles.forEach((file) => {
        let isValid = true;
        const fileErrors: string[] = [];

        // Check file type
        // if (allowedTypes.length > 0) {
        //   const isAllowedType = allowedTypes.some(type => file.type.includes(type));
        //   if (!isAllowedType) {
        //     fileErrors.push(`File type "${file.type}" is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
        //     isValid = false;
        //   }
        // }

        // Check file size
        if (file.size > maxSize) {
          fileErrors.push(
            `File "${file.name}" (${formatFileSize(file.size)}) exceeds maximum size of ${formatFileSize(maxSize)}`,
          );
          isValid = false;
        }

        // Check total file count (for append mode)
        if (mode === "append") {
          const totalAfterAdd = currentFiles.length + validFiles.length + 1;
          if (totalAfterAdd > maxFiles) {
            fileErrors.push(`Cannot add "${file.name}". Maximum ${maxFiles} files allowed`);
            isValid = false;
          }
        }

        // Check for duplicates (only if duplicates are not allowed)
        const isDuplicate = currentFiles.some((existingFile) => existingFile.name === file.name);
        if (isDuplicate && mode === "append" && !allowDuplicates) {
          fileErrors.push(`File "${file.name}" already exists`);
          isValid = false;
        }

        if (isValid) {
          validFiles.push(file);
        } else {
          rejectedFiles.push(file);
          errors.push(...fileErrors);
        }
      });

      // Check max files for replace mode
      if (mode === "replace" && validFiles.length > maxFiles) {
        const excess = validFiles.length - maxFiles;
        const excessFiles = validFiles.splice(maxFiles);
        rejectedFiles.push(...excessFiles);
        errors.push(
          `Only first ${maxFiles} files will be kept. ${excess} files rejected due to limit.`,
        );
      }

      const result = {
        isValid: errors.length === 0,
        errors,
        validFiles,
        rejectedFiles,
      };
      return result;
    },
    [allowedTypes, maxSize, maxFiles, mode, files, formatFileSize, allowDuplicates],
  );

  // Validate current file set (for post-operation validation)
  const validateCurrentFiles = useCallback(
    (currentFiles: File[]): FileManagerValidation => {
      const errors: string[] = [];
      const validFiles: File[] = [];
      const rejectedFiles: File[] = [];

      // Check total file count constraints
      if (currentFiles.length > maxFiles) {
        errors.push(
          `Maximum ${maxFiles} files allowed. Currently have ${currentFiles.length} files.`,
        );
        // Mark excess files as rejected
        rejectedFiles.push(...currentFiles.slice(maxFiles));
        validFiles.push(...currentFiles.slice(0, maxFiles));
      } else {
        validFiles.push(...currentFiles);
      }

      // Check for duplicates (only if duplicates are not allowed)
      if (!allowDuplicates) {
        const seen = new Set<string>();
        currentFiles.forEach((file) => {
          if (seen.has(file.name)) {
            errors.push(`Duplicate file name: "${file.name}"`);
          } else {
            seen.add(file.name);
          }
        });
      }

      // Check individual file constraints
      currentFiles.forEach((file) => {
        // Check file size
        if (file.size > maxSize) {
          errors.push(
            `File "${file.name}" (${formatFileSize(file.size)}) exceeds maximum size of ${formatFileSize(maxSize)}`,
          );
        }
      });

      return {
        isValid: errors.length === 0,
        errors,
        validFiles,
        rejectedFiles,
      };
    },
    [maxFiles, allowDuplicates, maxSize, formatFileSize],
  );

  // Add files with validation
  const addFiles = useCallback(
    (newFiles: File[]) => {
      if (skipValidation) {
        // Skip validation, add files directly
        if (mode === "replace") {
          setFiles(newFiles);
        } else {
          setFiles((prev) => [...prev, ...newFiles]);
        }
        setError(null);
        return {
          isValid: true,
          errors: [],
          validFiles: newFiles,
          rejectedFiles: [],
        };
      }

      const validation = validateFiles(newFiles);
      setError(validation.errors.join(", "));
      if (validation.validFiles.length > 0) {
        if (mode === "replace") {
          setFiles(validation.validFiles);
        } else {
          // For append mode, just add the valid files (validation already checked duplicates)
          setFiles((prev) => [...prev, ...validation.validFiles]);
        }
      }

      return validation;
    },
    [validateFiles, mode, skipValidation],
  );

  // Replace file at specific index
  const replaceFileAtIndex = useCallback(
    (index: number, newFile: File) => {
      if (skipValidation) {
        // Skip validation, replace file directly
        setFiles((prev) => {
          const updated = [...prev];
          updated[index] = newFile;
          return updated;
        });
        setError(null);
        return {
          isValid: true,
          errors: [],
          validFiles: [newFile],
          rejectedFiles: [],
        };
      }

      // When replacing, exclude the file being replaced from duplicate check
      const filesWithoutReplaced = files.filter((_, i) => i !== index);
      const validation = validateFiles([newFile], filesWithoutReplaced);
      setError(validation.errors.join(", "));

      if (validation.validFiles.length > 0) {
        setFiles((prev) => {
          const updated = [...prev];
          updated[index] = validation.validFiles[0];
          return updated;
        });
      }

      return validation;
    },
    [validateFiles, files, skipValidation],
  );

  // Delete file by index
  const deleteFileAtIndex = useCallback(
    (index: number) => {
      if (index >= 0 && index < files.length) {
        const newFiles = files.filter((_, i) => i !== index);
        setFiles(newFiles);

        // Validate after delete if enabled
        if (!skipValidation && validateOnAllOperations) {
          const validation = validateCurrentFiles(newFiles);
          setError(validation.errors.join(", "));
        } else {
          // Clear error if not validating (user removed files, might fix issues)
          setError(null);
        }

        return true;
      }
      return false;
    },
    [files, skipValidation, validateOnAllOperations, validateCurrentFiles],
  );

  // Delete file by name or file object
  const deleteFile = useCallback(
    (fileToDelete: File | string) => {
      const fileName = typeof fileToDelete === "string" ? fileToDelete : fileToDelete.name;
      const index = files.findIndex((file) => file.name === fileName);
      return deleteFileAtIndex(index);
    },
    [files, deleteFileAtIndex],
  );

  // Clear all files
  const clearAllFiles = useCallback(() => {
    setFiles([]);
    setEditingIndex(null);

    // Validate after clear if enabled (will likely show "minimum files required" error)
    if (!skipValidation && validateOnAllOperations) {
      const validation = validateCurrentFiles([]);
      setError(validation.errors.join(", "));
    } else {
      // Clear error if not validating
      setError(null);
    }
  }, [skipValidation, validateOnAllOperations, validateCurrentFiles]);

  // Edit functionality
  const startEdit = useCallback(
    (index: number) => {
      if (index >= 0 && index < files.length) {
        setEditingIndex(index);
        fileUploadRef.current?.triggerUpload();
        return true;
      }
      return false;
    },
    [files.length],
  );

  const cancelEdit = useCallback(() => {
    setEditingIndex(null);
  }, []);

  // Handle file selection (works for both add and edit modes)
  const handleFileSelect = useCallback(
    (newFiles: File[]) => {
      if (editingIndex !== null && newFiles.length > 0) {
        // Edit mode - replace file
        const validation = replaceFileAtIndex(editingIndex, newFiles[0]);
        if (validation.isValid) {
          setEditingIndex(null);
        }
        return validation;
      }
      // Normal mode - add files
      return addFiles(newFiles);
    },
    [editingIndex, replaceFileAtIndex, addFiles],
  );

  // Move file (for reordering)
  const moveFile = useCallback(
    (fromIndex: number, toIndex: number) => {
      if (fromIndex >= 0 && fromIndex < files.length && toIndex >= 0 && toIndex < files.length) {
        const newFiles = [...files];
        const [movedFile] = newFiles.splice(fromIndex, 1);
        newFiles.splice(toIndex, 0, movedFile);
        setFiles(newFiles);

        // Validate after move if enabled (unlikely to cause issues, but for completeness)
        if (!skipValidation && validateOnAllOperations) {
          const validation = validateCurrentFiles(newFiles);
          setError(validation.errors.join(", "));
        }

        return true;
      }
      return false;
    },
    [files, skipValidation, validateOnAllOperations, validateCurrentFiles],
  );

  // Trigger upload programmatically
  const triggerUpload = useCallback(() => {
    fileUploadRef.current?.triggerUpload();
  }, []);

  // Get file info
  const getFileInfo = useCallback(() => {
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);
    return {
      count: files.length,
      totalSize,
      formattedTotalSize: formatFileSize(totalSize),
      maxFiles,
      remainingSlots: Math.max(0, maxFiles - files.length),
      isAtLimit: files.length >= maxFiles,
    };
  }, [files, maxFiles, formatFileSize]);

  return {
    // State
    files,
    error,
    editingIndex,
    isEditing: editingIndex !== null,

    // Refs
    fileUploadRef,

    // File operations
    addFiles,
    replaceFileAtIndex,
    deleteFileAtIndex,
    deleteFile,
    clearAllFiles,
    moveFile,

    // Edit operations
    startEdit,
    cancelEdit,
    handleFileSelect, // Main handler for FileUpload component

    // Utilities
    triggerUpload,
    validateFiles,
    validateCurrentFiles,
    getFileInfo,
    formatFileSize,
    options: {
      maxFiles,
      allowedTypes,
      maxSize,
      mode,
      allowDuplicates,
      skipValidation,
      validateOnAllOperations,
    },
  };
};
