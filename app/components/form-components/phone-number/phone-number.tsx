import { RDSPhoneInputProps, RDSPhoneInput } from "@roshn/ui-kit";
import { useState } from "react";
import { FieldValues, Path, Control, Controller } from "react-hook-form";

import DirectionProvider, {
  Direction,
} from "~/features/common/direction-provider/direction-provider";

export type CountryType = {
  countryCode: string;
  dialCode: string;
  flag: string;
  name: string;
};

type ControlledPhoneInputProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  countries?: CountryType[];
  selectedCountryCode?: string;
  dir?: Direction;
} & Omit<
  RDSPhoneInputProps,
  "name" | "value" | "onChange" | "onBlur" | "selectedCountry" | "selectedCountryFlag"
>;

const defaultCountries: CountryType[] = [
  {
    countryCode: "ad",
    dialCode: "+376",
    flag: "https://flagcdn.com/w320/ad.png",
    name: "Andorra",
  },
  {
    countryCode: "ae",
    dialCode: "+971",
    flag: "https://flagcdn.com/w320/ae.png",
    name: "United Arab Emirates",
  },
  {
    countryCode: "af",
    dialCode: "+93",
    flag: "https://flagcdn.com/w320/af.png",
    name: "Afghanistan",
  },
  {
    countryCode: "ag",
    dialCode: "+1",
    flag: "https://flagcdn.com/w320/ag.png",
    name: "Antigua and Barbuda",
  },
  {
    countryCode: "ai",
    dialCode: "+1",
    flag: "https://flagcdn.com/w320/ai.png",
    name: "Anguilla",
  },
  {
    countryCode: "al",
    dialCode: "+355",
    flag: "https://flagcdn.com/w320/al.png",
    name: "Albania",
  },
  {
    countryCode: "sa",
    dialCode: "+966",
    flag: "https://flagcdn.com/w320/sa.png",
    name: "Saudi Arabia",
  },
  {
    countryCode: "my",
    dialCode: "+60",
    flag: "https://flagcdn.com/w320/my.png",
    name: "Malaysia",
  },
  {
    countryCode: "dz",
    dialCode: "+213",
    flag: "https://flagcdn.com/w320/dz.png",
    name: "Algeria",
  },
  {
    countryCode: "om",
    dialCode: "+968",
    flag: "https://flagcdn.com/w320/om.png",
    name: "Oman",
  },
  {
    countryCode: "qa",
    dialCode: "+974",
    flag: "https://flagcdn.com/w320/qa.png",
    name: "Qatar",
  },
  {
    countryCode: "sd",
    dialCode: "+249",
    flag: "https://flagcdn.com/w320/sd.png",
    name: "Sudan",
  },
  {
    countryCode: "es",
    dialCode: "+34",
    flag: "https://flagcdn.com/w320/es.png",
    name: "Spain",
  },
];

export function PhoneInput<T extends FieldValues>({
  name,
  control,
  countries = defaultCountries,
  selectedCountryCode = "sa",
  dir,
  helperText,
  ...rest
}: ControlledPhoneInputProps<T>) {
  const [selectedCountry, setSelectedCountry] = useState<CountryType>(
    countries.find((c) => c.countryCode === selectedCountryCode) ?? countries[0],
  );

  return (
    <DirectionProvider dir={dir}>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState }) => (
          <div dir={dir}>
            <RDSPhoneInput
              {...rest}
              value={field.value}
              onChange={(val) => {
                return field.onChange(typeof val === "string" ? val : "");
              }}
              onBlur={field.onBlur}
              countries={countries}
              selectedCountry={selectedCountry}
              onCountryChange={(country) => {
                setSelectedCountry(country);
              }}
              selectedCountryFlag={
                <img
                  src={selectedCountry.flag}
                  alt={selectedCountry.name}
                  width={16}
                  height={16}
                  css={{ borderRadius: "50%", objectFit: "cover" }}
                />
              }
              helperText={fieldState.error?.message ?? helperText}
              isInvalid={!!fieldState.error}
            />
          </div>
        )}
      />
    </DirectionProvider>
  );
}
