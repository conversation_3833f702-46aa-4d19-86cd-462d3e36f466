import { css } from "@emotion/react";
import { RDSRadio, RDSRadioGroup, AppTheme } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledRDSRadioGroupProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  caption?: string;
  direction?: "vertical" | "horizontal";
  required?: boolean;
  options: { value: string; label: string }[];
};

const styles = {
  radio: (theme: AppTheme) =>
    css({
      "& div": {
        color: theme.rds.color.text.ui.primary,
        position: "relative",
      },
      "& div > div > p": {
        ...theme?.rds?.typographies.label.md,
      },
      "&  div > div > p::after": {
        fontSize: "1.1rem",
        content: '" *"',
        color: theme?.rds?.color.text.functional.danger.tertiary,
      },
    }),
};

export function RadioGroup<T extends FieldValues>({
  name,
  control,
  label,
  caption,
  direction = "horizontal",
  required,
  options,
  ...rest
}: ControlledRDSRadioGroupProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      rules={{ required }}
      render={({ field }) => (
        <RDSRadioGroup
          css={styles.radio}
          label={label}
          caption={caption}
          direction={direction}
          required={required}
          onValueChange={(e) => {
            field.onChange(e);
            field.onBlur();
          }}
          value={field.value}
          {...rest}
        >
          {options.map((option) => (
            <RDSRadio key={option.value} value={option.value} label={option.label} />
          ))}
        </RDSRadioGroup>
      )}
    />
  );
}
