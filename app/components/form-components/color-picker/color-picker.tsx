import { css } from "@emotion/react";
import {
  AppTheme,
  RDSInput,
  RDSRadio,
  RDSRadioRoot,
  RDSTextInput,
  RDSTypography,
} from "@roshn/ui-kit";
import { HexColorPicker } from "react-colorful";
import { Controller, Control, FieldValues } from "react-hook-form";

import { Popover } from "~/components/popover/popover";

const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return { r: 0, g: 0, b: 0 };
  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
  };
};

const rgbToHex = (r: number, g: number, b: number) => {
  return `#${[r, g, b]
    .map((x) => {
      const hex = x.toString(16);
      return hex.length === 1 ? `0${hex}` : hex;
    })
    .join("")}`;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      alignItems: "flex-start",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],

      "& .react-colorful": {
        width: "100%",
        height: "270px",
      },

      "& .react-colorful__saturation": {
        borderRadius: "8px",
      },

      "& .react-colorful__pointer": {
        height: "32px",
        width: "32px",
        border: `8px solid ${theme.rds.color.base.white}`,
      },

      "& .react-colorful__last-control ": {
        height: "12px",
        borderRadius: "8px",
        marginBlockStart: "24px",
        marginBlockEnd: "14px",
      },
    }),

  pickerWrapper: css({
    display: "flex",
    flexDirection: "column",
    width: "100%",
    gap: "24px",
  }),

  inputGroup: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "row",
      width: "100%",
      gap: theme.rds.dimension["200"],
    }),

  label: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
    }),

  caption: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.sm,
      color: theme.rds.color.text.ui.tertiary,
    }),

  required: (theme: AppTheme) =>
    css({
      color: theme.rds.color.text.functional.danger.tertiary,
    }),

  header: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),
};

type ColorPickerProps = {
  defaultValue?: string;
  value?: string;
  onChange: (value: string) => void;
};

function ColorPickerPalette({ onChange, value = "#ffffff" }: ColorPickerProps) {
  const rgb = hexToRgb(value || "#ffffff");

  const handleRgbChange = (key: "r" | "g" | "b", val: number) => {
    const updated = { ...rgb, [key]: val };
    const hex = rgbToHex(updated.r, updated.g, updated.b);
    onChange?.(hex);
  };

  return (
    <div css={styles.wrapper}>
      <div css={styles.pickerWrapper}>
        <HexColorPicker defaultValue={value} color={value} onChange={onChange} />
        <div css={styles.inputGroup}>
          <RDSTextInput value={value} onChange={(e) => onChange(e.target.value)} label="Hex" />
          <RDSTextInput
            type="number"
            value={rgb.r}
            onChange={(e) => handleRgbChange("r", +e.target.value)}
            min={0}
            max={255}
            label="R"
          />

          <RDSTextInput
            type="number"
            value={rgb.g}
            onChange={(e) => handleRgbChange("g", +e.target.value)}
            min={0}
            max={255}
            label="G"
          />

          <RDSTextInput
            type="number"
            value={rgb.b}
            onChange={(e) => handleRgbChange("b", +e.target.value)}
            min={0}
            max={255}
            label="B"
          />
        </div>
      </div>
    </div>
  );
}

export const ColorPicker = ({
  color = "#986A4C",
  setColor,
  label,
  caption,
  required,
}: {
  color: string;
  setColor: (color: string) => void;
  label?: string;
  caption?: string;
  required?: boolean;
}) => {
  return (
    <div style={{ display: "flex", gap: "1rem", flexDirection: "column" }}>
      <div css={styles.header}>
        {label && (
          <RDSTypography css={styles.label}>
            {label}
            {required && <span css={styles.required}>*</span>}
          </RDSTypography>
        )}
        {caption && <RDSTypography css={styles.caption}>{caption}</RDSTypography>}
      </div>
      <Popover
        config={
          {
            placement: "bottom-start",
            possiblePlacements: ["bottom-start"],
            snap: false,
          } as any
        }
        trigger={
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              gap: "16px",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <RDSRadioRoot>
              <RDSRadio
                checked
                value={color}
                css={{
                  "span::after": { backgroundColor: color },
                  "span:hover::after": { backgroundColor: color },
                  "&[data-state=checked]": {
                    borderColor: color,
                  },
                  "&:hover[data-state=checked]": {
                    borderColor: color,
                  },
                }}
              />
            </RDSRadioRoot>
            <RDSInput value={color} onChange={(e) => setColor?.(e.target.value)} />
          </div>
        }
      >
        <div
          style={{
            borderRadius: "8px",
            background: "#FFF",
            boxShadow: "0 16px 24px 0 rgba(0, 0, 0, 0.10)",
            padding: "16px",
            width: "550px",
          }}
        >
          <ColorPickerPalette value={color} onChange={(hex) => setColor?.(hex)} />
        </div>
      </Popover>
    </div>
  );
};

export const ColorPickerFormComponent = ({
  control,
  name,
  label,
  caption,
  isRequired,
}: {
  control: Control<FieldValues>;
  name: string;
  label?: string;
  caption?: string;
  isRequired?: boolean;
}) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <ColorPicker
          color={field.value}
          setColor={field.onChange}
          label={label}
          required={isRequired}
          caption={caption}
        />
      )}
    />
  );
};
