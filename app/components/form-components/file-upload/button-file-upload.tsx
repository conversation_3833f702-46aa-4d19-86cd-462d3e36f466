import { css, useTheme } from "@emotion/react";
import {
  AppTheme,
  R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  RDSButton,
  RDSIconButton,
  RDSModal,
  RDSSelect,
  RDSTypography,
  RDSUploadFile,
} from "@roshn/ui-kit";
import isEmpty from "lodash/isEmpty";
import isEqual from "lodash/isEqual";
import { AssetWrapper } from "node_modules/@roshn/ui-kit/dist/components/rds-components/asset-wrapper";
import { useEffect, useState } from "react";
import {
  Control,
  useController,
  UseFormSetError,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";

import { createSvg } from "~/components/svgs";
import { formatAsFormData } from "~/utils/format-as-form-data";

const Trash = createSvg(() => import("~/assets/icons/trash.svg"));
const Upload = createSvg(() => import("~/assets/icons/upload.svg"));
const Cross = createSvg(() => import("~/assets/icons/x-mark.svg"));
const File = createSvg(() => import("~/assets/icons/file.svg"));
const Image = createSvg(() => import("~/assets/icons/image-gallery.svg"));
const Eye = createSvg(() => import("~/assets/icons/black-eye.svg"));

type FileUploadProps = {
  name: string;
  control: Control;
  label?: string;
  accept?: string;
  multiple?: boolean;
  btnLabel?: string;
  helperText?: string;
  onFormSubmit: () => {};
  options?: { label: string; value: string }[];
  setValue: UseFormSetValue<Record<string, any>>;
  watch: UseFormWatch<Record<string, any>>;
  setError: UseFormSetError<Record<string, any>>;
};

type Category = "master-plane" | "brochure" | "floor-plane" | null;

const styles = {
  row: (theme: AppTheme, isLast: boolean) =>
    css({
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingInline: "16px",
      paddingBlock: "8px",
      borderBottom: isLast
        ? "none"
        : `1px solid ${theme.rds.color.border.brand.secondary.inverse.default}`,
      backgroundColor: "#fff",
      height: "fit-content",

      "& > span": {
        width: "20%",
        textAlign: "left",
        justifyContent: "flex-start",
      },
    }),

  ellipsis: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      width: "100%",
      textAlign: "left",
      justifyContent: "flex-start",
      overflow: "hidden",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
      color: theme.rds.color.text.ui.tertiary,
    }),

  category: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.emphasis.md,
      textTransform: "capitalize",
    }),

  tableTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      display: "flex",
      gap: theme.rds.dimension["100"],
      justifyContent: "center",
      alignItems: "center",
      color: theme.rds.color.text.ui.tertiary,
    }),

  emptyWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      marginBlock: theme.rds.dimension["500"],
      gap: theme.rds.dimension["200"],
      width: "100%",
      alignItems: "center",
    }),

  name: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["100"],
      width: "20%",
      alignItems: "center",
    }),

  asset: css({
    cursor: "pointer",
  }),

  headingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary.primary,
    }),

  descriptionText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.primary.secondary,
      textAlign: "center",
    }),

  errorText: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.functional.danger.tertiary,
    }),

  uploadWrapper: css({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  }),

  uploadInfoWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  uploadDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
    }),

  uploadDesExample: (theme: AppTheme) =>
    css({
      ...theme.rds.brand.font.fontFamily.label,
      ...theme.rds.font.fontSize["75"],
      color: theme.rds.color.text.ui.tertiary,
    }),

  fileList: css({
    marginTop: "16px",
    display: "flex",
    flexDirection: "column",
    gap: "8px",
  }),

  fileItem: css({
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    border: "1px solid #eee",
    borderRadius: "4px",
    padding: "8px 12px",
  }),

  removeButton: css({
    cursor: "pointer",
    width: "16px",
    height: "16px",
  }),

  modalDimension: css({
    "& div": {
      maxWidth: "640px",
    },
  }),

  modalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["300"],
      justifyContent: "center",
      alignItems: "center",
    }),

  modalTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h4,
    }),

  modalContentWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      width: "576px",
    }),

  button: css({
    height: "16px",
    width: "16px",
    position: "absolute",
    right: "27px",
    top: "24px",

    "& path": {
      stroke: "none",
    },
    ":hover": {
      "& path": {
        stroke: "none",
      },
    },
  }),

  primaryWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),
  tableWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  header: (theme: AppTheme) =>
    css({
      paddingInline: "16px",
      paddingBlock: "8px",
      height: theme?.rds?.dimension["500"],
      alignItems: "center",
      display: "flex",
      backgroundColor: theme.rds.color.background.ui.tertiary.default,
      justifyContent: "space-between",
    }),

  headerTypo: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      width: "20%",
      textAlign: "left",
    }),
};

const selectOptions = [
  { label: "Master plane", value: "masterplan" },
  { label: "Brochures", value: "brochures" },
  { label: "Floor plans", value: "floor-plans" },
];

function extractWatchedValues<T extends Record<string, any>>(
  watched: T,
  keys = ["masterplan", "brochures", "floor-plans"],
): Record<string, any> {
  return Object.fromEntries(keys.filter((key) => watched[key]).map((key) => [key, watched[key]]));
}

export function ButtonFileUpload({
  btnLabel = "Upload File",
  label,
  helperText,
  options = selectOptions,
  name,
  setValue,
  watch,
  control,
  setError,
  ...rest
}: FileUploadProps) {
  const theme = useTheme();

  const {
    fieldState: { error, isTouched },
  } = useController({ name, control });

  const watched = watch()[name] || {};

  const value: Record<string, any> = extractWatchedValues(watched) ?? {};

  const [showUpload, setShowUpload] = useState(false);
  const [category, setCategory] = useState<Category>(null);

  const [files, setFiles] = useState<Record<string, File>>(value);

  const [file, setFile] = useState<File | null>(null);

  const handleButtonClick = () => {
    setShowUpload(!showUpload);
  };

  const details = (slug: string) => rest?.attachments.find((itm) => itm.slug == slug);

  useEffect(() => {
    const emptyFiles = isEmpty(files);

    if (emptyFiles && isTouched) {
      setError(name, { message: "Please add files" });
    }

    if (!emptyFiles) {
      setValue(
        name,
        Object.entries(files).map(([key, value]) =>
          formatAsFormData(value, { asset_id: details(key).id }),
        ),
        {
          shouldValidate: true,
          ...(!isEqual(value, files) ? { shouldDirty: true, shouldTouch: true } : {}),
        },
      );
    }
  }, [files]);

  const handleRemove = (categoryToRemove: string) => {
    setFiles((prev) => {
      const newFiles = { ...prev };
      delete newFiles[categoryToRemove];
      return newFiles;
    });
  };

  const columns = ["Category", "Name", "Size", "Actions"];

  const tableData = Object?.entries(files ?? {}).map(([key, value], idx) => ({
    category: key,
    name: value?.name,
    size: value?.size,
    actions: "actions",
  }));

  const iconMap = {
    brochures: File,
    masterplan: Image,
    "floor-plans": Image,
  };

  function formatTitle(str: string) {
    if (!str) return "";
    const withSpaces = str.replace(/-/g, " ");
    return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1);
  }

  return (
    <>
      <div css={styles.modalDimension}>
        <RDSModal
          isOpen={showUpload}
          showContent
          header={false}
          content={
            <div css={styles.modalWrapper}>
              <div>
                <RDSTypography css={styles.modalTypo}>Upload attachment</RDSTypography>
                <RDSIconButton
                  css={styles.button}
                  onClick={() => setShowUpload(false)}
                  variant="tertiary"
                  icon={<Cross />}
                />
              </div>
              <div css={styles.modalContentWrapper}>
                <RDSSelect
                  onChange={(e) => {
                    setCategory(e.value);
                  }}
                  label="Category"
                  isRequired
                  placeholder="Choose a category of file..."
                  options={selectOptions}
                />
                <RDSUploadFile
                  label="Select file to upload"
                  isRequired
                  caption="JPG, PNG, WEBP or PDF less than 5MB"
                  disabled={!category}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                    const file = event?.target?.files?.[0] ?? null;
                    setFile(file);
                  }}
                />
              </div>
              <RDSButton
                disabled={!file}
                variant="primary"
                onClick={() => {
                  setFiles((prev) => ({ ...prev, [category]: file }));
                  setFile(null);
                  setCategory(null);
                  handleButtonClick();
                }}
                text="UPLOAD ATTACHMENT"
              />
            </div>
          }
        />
      </div>
      <div>
        <div css={styles.uploadWrapper}>
          <div css={styles.uploadInfoWrapper}>
            {label && <RDSTypography css={styles.uploadDes}>{label}</RDSTypography>}
            {helperText && (
              <RDSTypography css={styles.uploadDesExample}>{helperText}</RDSTypography>
            )}
            {error && <RDSTypography css={styles.errorText}>Please add files</RDSTypography>}
          </div>
          <RDSButton
            type="button"
            css={{ textTransform: "none" }}
            variant="secondary"
            size="lg"
            text={btnLabel}
            data-testid="upload-button"
            onClick={handleButtonClick}
            leadIcon={
              <RDSAssetWrapper>
                <Upload />
              </RDSAssetWrapper>
            }
          />
        </div>

        <div data-testid="file-list" css={styles.fileList}>
          {Object.keys(files).length > 0 ? (
            <div css={styles.tableWrapper}>
              <div css={styles.header}>
                {columns.map((i) => (
                  <span css={styles.headerTypo}>{i}</span>
                ))}
              </div>
              {tableData.map(({ category, name, size }, idx) => {
                const Icon = iconMap[category] ?? iconMap["masterplan"];

                return (
                  <div key={name} css={styles.row(theme, idx === tableData.length - 1)}>
                    <span css={styles.category}>{formatTitle(category)}</span>
                    <div css={styles.name}>
                      <AssetWrapper>
                        <Icon />
                      </AssetWrapper>
                      <span css={styles.ellipsis}>{name}</span>
                    </div>
                    <span css={styles.tableTypo}>
                      {size > 1024 * 1024
                        ? `${(size / (1024 * 1024)).toFixed(2)} MB`
                        : `${(size / 1024).toFixed(2)} KB`}
                    </span>
                    <div css={styles.name}>
                      <AssetWrapper css={styles.asset} onClick={() => handleRemove(category)}>
                        <Trash />
                      </AssetWrapper>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div css={styles.emptyWrapper}>
              <RDSTypography css={styles.headingText}>No attachments yet</RDSTypography>
              <RDSTypography css={styles.descriptionText}>
                Upload files to share important project details — they will be organized by category
                for easy access.
              </RDSTypography>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
