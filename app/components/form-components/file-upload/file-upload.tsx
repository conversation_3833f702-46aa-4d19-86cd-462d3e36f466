import { RDSUploadFile, RDSUploadFileProps } from "@roshn/ui-kit";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledUploadFileProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
} & Omit<RDSUploadFileProps, "name" | "value" | "onChange">;

export const UploadFile = <T extends FieldValues>({
  name,
  control,
  ...rest
}: ControlledUploadFileProps<T>) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        return (
          <RDSUploadFile
            {...rest}
            defaultValue={field?.value}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              const file = event?.target?.files?.[0] ?? null;
              const files = field.value ? [...field.value] : [];
              files.push(file);
              field.onChange(files);
            }}
            onDismissHandler={(file: File) => {
              const files = field.value ? [...field.value] : [];
              const newFiles = files.filter((f) => f.name !== file.name);
              field.onChange(newFiles);
            }}
            validationMsg={fieldState.error?.message}
            validationType={fieldState.error ? "error" : rest.validationType}
          />
        );
      }}
    />
  );
};
