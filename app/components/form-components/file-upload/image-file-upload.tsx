import isEqual from "lodash/isEqual";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

import { ImageGalleryUpload } from "~/components/upload-image/image-gallery-upload";
import { ImageUpload } from "~/components/upload-image/image-upload";
import { FileSource } from "~/hooks/use-file-url";

type ControlledUploadFileProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
};

export const UploadImageField = <T extends FieldValues>({
  name,
  control,
  browseFilePlaceholder,
  caption,
  label,
  required,
  maxSize,
  ...rest
}: ControlledUploadFileProps<T> &
  Omit<React.ComponentProps<typeof ImageUpload>, "onChange" | "value">) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        const errorMessage = Array.isArray(fieldState.error)
          ? fieldState.error
              .filter(Boolean)
              .map((e) => e.message)
              .join(", ")
          : fieldState.error?.message;

        return (
          <ImageUpload
            value={field?.value}
            onChange={(e) => {
              field.onChange(e);
              if (!isEqual(e, field.value)) {
                field.onBlur();
              }
            }}
            browseFilePlaceholder={browseFilePlaceholder}
            caption={caption}
            maxSize={maxSize}
            label={label}
            required={required}
            skipFileManagerValidation
            validationMsg={errorMessage}
            validationType={fieldState.error ? "error" : undefined}
            {...rest}
          />
        );
      }}
    />
  );
};

export const UploadImageGalleryField = <T extends FieldValues>({
  name,
  control,
  browseFilePlaceholder,
  caption,
  label,
  required = true,
  maxSize = 5 * 1024 * 1024,
  maxFiles = 10,
  ...rest
}: ControlledUploadFileProps<T> &
  Omit<React.ComponentProps<typeof ImageGalleryUpload>, "onChange" | "value">) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        const files: FileSource[] = Array.isArray(field.value)
          ? field.value.map((v) => (typeof v === "string" ? v : v))
          : [];
        const errorMessage = Array.isArray(fieldState.error)
          ? fieldState.error
              .filter(Boolean)
              .map((e) => e.message)
              .join(", ")
          : fieldState.error?.message;
        return (
          <ImageGalleryUpload
            value={files}
            onChange={(e) => {
              field.onChange(e);
              field.onBlur();
            }}
            browseFilePlaceholder={browseFilePlaceholder}
            caption={caption}
            maxSize={maxSize}
            label={label}
            required={required}
            maxFiles={maxFiles}
            skipFileManagerValidation
            validationMsg={errorMessage}
            validationType={fieldState.error ? "error" : undefined}
            {...rest}
          />
        );
      }}
    />
  );
};
