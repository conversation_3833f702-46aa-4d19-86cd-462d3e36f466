import { css, SerializedStyles, useTheme } from "@emotion/react";
import { MinusIcon, PlusIcon } from "@radix-ui/react-icons";
import { AppTheme, RDSIconButton, RDSStepperProps, RDSTypography } from "@roshn/ui-kit";
import { useEffect, useRef } from "react";
import { Controller, Control, FieldValues, Path } from "react-hook-form";

type ControlledStepperProps<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  type?: string;
} & Omit<RDSStepperProps, "name" | "value" | "onChange" | "onBlur">;

const styles = {
  withLabelFieldWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      gap: theme?.rds?.dimension[100],
    }),
  fieldLabel: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.md,
      color: theme.rds.color.text.ui.primary,
      display: "flex",
      alignItems: "center",
      gap: theme?.rds?.dimension[50],
    }),
  inputField: (theme: AppTheme) =>
    css({
      width: theme.rds.dimension["700"],
      height: theme.rds.dimension["500"],
      textAlign: "center",
      border: `${theme.rds.border.borderWidth[100]} solid ${theme.rds.color.border.ui.primary}`,
      color: theme.rds.color.text.ui.primary,
      "&::placeholder": {
        color: theme.rds.color.text.ui.tertiary,
      },
    }),

  error: (theme: AppTheme) =>
    css({
      color: theme?.rds?.color.text.functional.danger.tertiary,
      fontSize: "12px",
      marginTop: "4px",
    }),

  counterWrapper: css({
    display: "flex",
    alignItems: "center",
    gap: "8px",
    marginBlockEnd: "4px",
  }),

  labelCustomStyles: css({
    flexDirection: "column",
    alignItems: "start",
  }),
};

const RenderFieldWithLabel = ({
  label,
  isRequired,
  children,
  customStyles,
}: {
  label: string;
  isRequired?: boolean;
  children: React.ReactNode;
  customStyles?: SerializedStyles;
}) => {
  const theme = useTheme() as AppTheme;

  return (
    <div css={[styles.withLabelFieldWrapper(theme), customStyles]}>
      <RDSTypography
        css={styles.fieldLabel(theme)}
        fontName={theme?.rds?.typographies?.label.md}
        color={theme?.rds?.color?.text?.ui?.primary}
      >
        {label}
        {isRequired && (
          <span css={{ color: theme?.rds?.color?.text?.functional?.danger?.tertiary }}>*</span>
        )}
      </RDSTypography>
      {children}
    </div>
  );
};

export function InputNumber<T extends FieldValues>({
  name,
  control,
  label,
  helperText,
  ...rest
}: ControlledStepperProps<T> & {
  label: string;
  helperText: string;
  isRequired?: boolean;
}) {
  const theme = useTheme() as AppTheme;

  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      const buttons = containerRef.current.querySelectorAll("button");
      buttons.forEach((button) => {
        if (!button.getAttribute("type")) {
          button.setAttribute("type", "button");
        }
      });
    }
  });

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => {
        const value = Number(field.value) || 0;
        return (
          <RenderFieldWithLabel
            label={label}
            isRequired={rest?.isRequired}
            customStyles={styles.labelCustomStyles}
          >
            <div ref={containerRef}>
              <div css={styles.counterWrapper}>
                <RDSIconButton
                  icon={<MinusIcon />}
                  variant="secondary"
                  onClick={() => {
                    const currentValue = value || 0;
                    const newValue = Math.max(0, currentValue - 1);
                    field.onChange(newValue);
                  }}
                />
                <input
                  css={styles.inputField(theme)}
                  value={value}
                  onChange={(e) => {
                    const newValue = parseInt(e.target.value, 10) || 0;
                    field.onChange(newValue);
                  }}
                  onBlur={field.onBlur}
                  min="0"
                />
                <RDSIconButton
                  icon={<PlusIcon />}
                  onClick={() => {
                    const currentValue = value || 0;
                    const newValue = currentValue + 1;
                    field.onChange(newValue);
                  }}
                />
              </div>
              <RDSTypography fontName={theme?.rds?.typographies?.label.sm}>
                {helperText}
              </RDSTypography>
              {fieldState.error && <div css={styles.error}>{fieldState.error.message}</div>}
            </div>
          </RenderFieldWithLabel>
        );
      }}
    />
  );
}
