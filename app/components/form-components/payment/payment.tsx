import { css, useTheme } from "@emotion/react";
import { AppTheme, RDSTextInput } from "@roshn/ui-kit";
import { useMemo } from "react";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import LabelWrapper from "~/components/ui/label-wrapper/label-wrapper";
import { Field } from "~/services/project/hooks/use-get-template";

const styles = {
  primaryWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),
  tableWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  header: (theme: AppTheme) =>
    css({
      paddingInline: "16px",
      paddingBlock: "8px",
      height: theme?.rds?.dimension["500"],
      alignItems: "center",
      display: "flex",
      backgroundColor: theme.rds.color.background.ui.tertiary.default,
    }),

  headerTypo: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      width: "50%",
      textAlign: "left",
    }),

  inputWrapper: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.label.emphasis.md,
      display: "flex",
      alignItems: "center",
    }),

  row: (theme: AppTheme, isLast: boolean) =>
    css({
      color: theme.rds.color.text.ui.primary,
      fontWeight: 500,
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      paddingInline: "16px",
      paddingBlock: "8px",
      borderBottom: isLast ? "none" : "1px solid #eee",
      backgroundColor: "#fff",
      height: "fit-content",

      "& > div": {
        width: "50%",
        textAlign: "left",
      },
    }),

  bottomInpWrapper: css({
    width: "50%",
    alignSelf: "end",
  }),
};

export function Payment({
  label,
  child = [],
  control,
  columns,
  ...rest
}: {
  label: string;
  columns: string[];
  child: Field[];
}) {
  const theme = useTheme();

  const watchedValues = rest?.watch((child ?? []).map((i) => i.name));

  const sum = useMemo(() => {
    const [downPayment, construction, handover] = watchedValues.map((v) =>
      isNaN(v) ? 0 : Number(v),
    );
    return downPayment + construction + handover;
  }, [watchedValues]);

  return (
    <LabelWrapper label={label}>
      <div css={styles.primaryWrapper}>
        <div css={styles.tableWrapper}>
          <div css={styles.header}>
            {columns.map((i) => (
              <span css={styles.headerTypo}>{i}</span>
            ))}
          </div>
          {child.map(({ label, ...config }, idx) => (
            <div key={label} css={styles.row(theme, idx === child.length - 1)}>
              {label && <span>{label}</span>}
              <div css={styles.inputWrapper}>
                <FieldRenderer control={control} {...config} />
              </div>
            </div>
          ))}
        </div>
        <div css={styles.bottomInpWrapper}>
          <RDSTextInput
            suffixText="%"
            type="text"
            disabled
            label="Total amount to be paid"
            helperText="This number needs to be 100."
            value={sum}
          />
        </div>
      </div>
    </LabelWrapper>
  );
}
