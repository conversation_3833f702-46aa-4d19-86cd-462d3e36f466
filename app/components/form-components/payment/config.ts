export const config = {
  custom_attributes: [
    {
      attribute_type: "RESERVATION_FEE",
      label: "Reservation fee",
      section: "payment-plan",
      child: [
        {
          id: 26,
          name: "reservation fee refundable",
          slug: "reservation-fee-refundable",
          attribute_type: "BOOLEAN",
          options: [],
          is_required: true,
          order: 9,
          scope: "CATEGORY",
          section: "payment",
          locale: "en",
          label: "Refundable?",
          placeholder: null,
          helper_text: "",
          custom_attribute_field_key: "reservation_fee_refundable",
          option_meta: [],
        },
        {
          id: 27,
          name: "reservation fee type",
          slug: "reservation-fee-type",
          attribute_type: "BOOLEAN",
          options: ["Fixed", "Percentage"],
          is_required: true,
          order: 10,
          scope: "CATEGORY",
          section: "payment",
          locale: "en",
          label: "Fee Type",
          placeholder: null,
          helper_text: "",
          custom_attribute_field_key: "reservation_fee_type",
          option_meta: [],
        },
        {
          id: 28,
          name: "reservation fee ammount",
          slug: "reservation-fee-ammount",
          attribute_type: "CURRENCY",
          options: [],
          is_required: true,
          order: 11,
          scope: "CATEGORY",
          section: "payment",
          locale: "en",
          label: "Reservation fee amount (SAR)",
          placeholder: "Enter price...",
          helper_text: "Enter the full amount to be paid for the reservation.",
          custom_attribute_field_key: "reservation_fee_ammount",
          option_meta: [],
        },
      ],
    },
    {
      attribute_type: "PAYMENT_PLAN",
      id: 1,
      label: "Payment Schedule",
      section: "payment-plan",
      option: {},
      child: [
        {
          id: 29,
          name: "down payment",
          slug: "down-payment",
          attribute_type: "PERCENTAGE",
          options: [],
          is_required: true,
          order: 12,
          scope: "CATEGORY",
          section: "payment-schedule",
          locale: "en",
          label: "Down payment",
          placeholder: "33.33",
          helper_text: "",
          custom_attribute_field_key: "down_payment",
        },

        {
          id: 31,
          name: "on handover",
          slug: "on-handover",
          attribute_type: "PERCENTAGE",
          options: [],
          is_required: true,
          order: 14,
          scope: "CATEGORY",
          section: "payment-schedule",
          locale: "en",
          label: "On handover",
          placeholder: "33.33",
          helper_text: "",
          custom_attribute_field_key: "on_handover",
          option_meta: [],
        },
        {
          id: 30,
          name: "during construction",
          slug: "during-construction",
          attribute_type: "PERCENTAGE",
          options: [],
          is_required: true,
          order: 13,
          scope: "CATEGORY",
          section: "payment-schedule",
          locale: "en",
          label: "During construction",
          placeholder: "33.33",
          helper_text: "",
          custom_attribute_field_key: "during_construction",
          option_meta: [],
        },
      ],
      option_meta: ["c1", "c2"],
    },
  ],
};
