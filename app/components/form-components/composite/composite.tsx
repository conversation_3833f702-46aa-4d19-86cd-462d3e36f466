import { ControllerProps } from "react-hook-form";

import { FieldRenderer } from "~/components/field-renderer/field-renderer";
import LabelWrapper from "~/components/ui/label-wrapper/label-wrapper";
import { Field } from "~/services/project/hooks/use-get-template";

export function Composite({
  label,
  child = [],
  control,
}: ControllerProps & { label: string; child: Field[] }) {
  return (
    <LabelWrapper label={label}>
      {child.map((field: Field) => (
        <FieldRenderer key={field.name} control={control} {...field} />
      ))}
    </LabelWrapper>
  );
}
