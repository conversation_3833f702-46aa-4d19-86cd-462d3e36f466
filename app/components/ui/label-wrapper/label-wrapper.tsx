import { css } from "@emotion/react";
import { AppTheme, RDSTypography } from "@roshn/ui-kit";
import React from "react";

const styles = {
  typography: (theme: AppTheme) =>
    css({
      ...theme?.rds?.typographies?.body.emphasis.md,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  wrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),
};

const LabelWrapper = ({ label, children }: { label: string; children: React.ReactNode }) => {
  return (
    <div css={styles.wrapper}>
      <RDSTypography css={styles.typography}>{label}</RDSTypography>
      {children}
    </div>
  );
};

export default LabelWrapper;
