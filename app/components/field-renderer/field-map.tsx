import { RDSUploadFile } from "@roshn/ui-kit";

import { ColorPickerFormComponent } from "../form-components/color-picker/color-picker";
import { Composite } from "../form-components/composite/composite";
import { DatePicker } from "../form-components/date-picker/date-picker";
import { RichTextEditor } from "../form-components/editor/editor";
import { ButtonFileUpload } from "../form-components/file-upload/button-file-upload";
import {
  UploadImageField,
  UploadImageGalleryField,
} from "../form-components/file-upload/image-file-upload";
import { Input } from "../form-components/input/input";
import { InputNumber } from "../form-components/input-number/input-number";
import { Payment } from "../form-components/payment/payment";
import { PhoneInput } from "../form-components/phone-number/phone-number";
import { RadioGroup } from "../form-components/radio-button/radio-button";
import { Select } from "../form-components/select/select";
import { TagSelector } from "../form-components/tag-selector/tags-selector";
import { TextArea } from "../form-components/text-area/text-area";

const FallBack = () => null;

export enum FieldType {
  TEXT = "TEXT",
  TEXTAREA = "TEXTAREA",
  MULTI_SELECT = "MULTI_SELECT",
  NUMBER = "NUMBER",
  IMAGE = "IMAGE",
  DATE = "DATE",
  BOOLEAN = "BOOLEAN",
  EDITOR = "EDITOR",
  UPLOAD = "UPLOAD",
  GALLERY = "GALLERY",
  PHONE_NUMBER = "PHONE_NUMBER",
  UPLOAD_FILE = "UPLOAD_FILE",
  COLOR_PICKER = "COLOR_PICKER",
  COUNTER = "COUNTER",
  SELECT = "SELECT",
  PERCENTAGE = "PERCENTAGE",
  CURRENCY = "CURRENCY",
  DEFAULT = "DEFAULT",
  COMPOSITE = "COMPOSITE",
  PAYMENT = "PAYMENT",
}

export const fieldMap: Record<FieldType, any> = {
  [FieldType.TEXT]: Input,
  [FieldType.TEXTAREA]: TextArea,
  [FieldType.MULTI_SELECT]: TagSelector,
  [FieldType.NUMBER]: Input,
  [FieldType.IMAGE]: RDSUploadFile,
  [FieldType.DATE]: DatePicker,
  [FieldType.BOOLEAN]: RadioGroup,
  [FieldType.EDITOR]: RichTextEditor,
  [FieldType.UPLOAD]: ButtonFileUpload,
  [FieldType.GALLERY]: UploadImageGalleryField,
  [FieldType.PHONE_NUMBER]: PhoneInput,
  [FieldType.UPLOAD_FILE]: UploadImageField,
  [FieldType.COLOR_PICKER]: ColorPickerFormComponent,
  [FieldType.COUNTER]: InputNumber,
  [FieldType.SELECT]: Select,
  [FieldType.PERCENTAGE]: Input,
  [FieldType.CURRENCY]: Input,
  [FieldType.DEFAULT]: FallBack,
  [FieldType.COMPOSITE]: Composite,
  [FieldType.PAYMENT]: Payment,
};
