import { css } from "@emotion/react";
import { AppTheme } from "@roshn/ui-kit";
import { Control } from "react-hook-form";

import { fieldMap, FieldType } from "./field-map";

type FieldSchema = {
  type: FieldType;
  name: string;
  control: Control;
  label?: string;
  placeholder?: string;
  options?: { label: string; value: string }[];
  attribute_type: FieldType;
  [key: string]: any;
  child?: FieldSchema[];
};

const styles = {
  half: (theme: AppTheme) =>
    css({
      width: `calc(50% - ${theme.rds.dimension["100"]})`,
    }),

  full: css({
    width: "100%",
  }),
};

export const FieldRenderer = (props: FieldSchema) => {
  const Component = fieldMap[props?.attribute_type ?? "DEFAULT"] ?? fieldMap["DEFAULT"];

  const halfWidthComponents = [
    FieldType.TEXT,
    FieldType.COUNTER,
    FieldType.NUMBER,
    FieldType.DATE,
    FieldType.PHONE_NUMBER,
    FieldType.CURRENCY,
    FieldType.SELECT,
  ];

  if (!Component) {
    console.warn(`No component found for field type: ${props.attribute_type}`);
    return null;
  }

  const isHalf = halfWidthComponents.includes(props.attribute_type);

  return (
    <div css={isHalf ? styles.half : styles.full}>
      <Component {...(props as any)} />
    </div>
  );
};
