import React, { useState } from "react";
import { UseLayerProps } from "react-laag";

import { usePopover } from "~/hooks/use-popover";

export const Popover = ({
  children,
  trigger,
  config,
}: {
  children: React.ReactNode;
  trigger: React.ReactNode;
  config?: UseLayerProps;
}) => {
  const [open, setOpen] = useState<boolean>(false);

  const { triggerProps, layerProps, renderLayer } = usePopover({
    config,
    open,
    setOpen,
  });

  return (
    <div>
      <div
        onClick={() => setOpen(!open)}
        {...triggerProps}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            setOpen(!open);
          }
        }}
      >
        {trigger}
      </div>

      {open &&
        renderLayer(
          <div
            css={{
              backgroundColor: "white",
              width: "fit-content",
              padding: "5px",
            }}
            {...layerProps}
          >
            {children}
          </div>,
        )}
    </div>
  );
};
