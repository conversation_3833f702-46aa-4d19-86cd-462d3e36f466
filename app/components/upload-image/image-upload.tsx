import { useMemo } from "react";

import {
  buildViewerComponent,
  buildFileUploadComponent,
} from "~/components/rich-file-upload/factories";

export const ImageUpload = ({
  onChange,
  value,
  browseFilePlaceholder,
  label,
  caption,
  required = true,
  maxSize = 5 * 1024 * 1024,
  skipFileManagerValidation = false,
  validationMsg,
  validationType,
  ...rest
}: {
  onChange: (files: File[]) => void;
  value: File[];
  browseFilePlaceholder: string;
  label: string;
  caption?: string;
  required?: boolean;
  maxSize?: number;
  skipFileManagerValidation?: boolean;
  validationMsg?: string;
  validationType?: string;
}) => {
  const ViewerComponent = useMemo(() => buildViewerComponent({ type: "single-image" }), []);

  const FileUploadComponent = useMemo(
    () =>
      buildFileUploadComponent({
        label,
        fileOptions: {
          maxFiles: 1,
          allowedTypes: ["image"],
          maxSize,
          mode: "replace",
          allowDuplicates: false,
          skipValidation: skipFileManagerValidation,
        },
        allowEditing: true,
        browseFilePlaceholder,
        required,
        renderViewer: ViewerComponent,
        caption,
      }),
    [label, maxSize, skipFileManagerValidation, browseFilePlaceholder, required, caption],
  );

  return (
    <FileUploadComponent
      onChange={onChange}
      value={value}
      error={rest}
      validationMsg={validationMsg}
      validationType={validationType}
    />
  );
};
