import { useMemo } from "react";

import {
  buildViewerComponent,
  buildFileUploadComponent,
} from "~/components/rich-file-upload/factories";

export const ImageGalleryUpload = ({
  onChange,
  value,
  browseFilePlaceholder,
  label,
  caption,
  required = true,
  maxFiles = 5,
  maxSize = 5 * 1024 * 1024,
  skipFileManagerValidation = false,
  validationMsg,
  validationType,
  ...rest
}: {
  onChange: (files: File[]) => void;
  value: File[];
  browseFilePlaceholder: string;
  label: string;
  caption?: string;
  required?: boolean;
  maxSize?: number;
  maxFiles: number;
  skipFileManagerValidation?: boolean;
  validationMsg?: string;
  validationType?: string;
}) => {
  const ViewerComponent = useMemo(() => buildViewerComponent({ type: "multiple-images" }), []);
  const FileUploadComponent = useMemo(
    () =>
      buildFileUploadComponent({
        label,
        fileOptions: {
          maxFiles,
          allowedTypes: ["image"],
          maxSize,
          mode: "append",
          allowDuplicates: false,
          skipValidation: skipFileManagerValidation,
        },
        allowEditing: false,
        browseFilePlaceholder,
        required,
        renderViewer: ViewerComponent,
        caption,
      }),
    [label, maxFiles, maxSize, skipFileManagerValidation, browseFilePlaceholder, required, caption],
  );

  return (
    <FileUploadComponent
      onChange={onChange}
      value={value}
      error={rest}
      validationMsg={validationMsg}
      validationType={validationType}
    />
  );
};
