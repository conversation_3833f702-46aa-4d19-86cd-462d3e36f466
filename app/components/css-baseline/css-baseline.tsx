import { Global } from "@emotion/react";

import FSAlbertArabic_Bold_Woff2 from "~/assets/fonts/FSAlbertArabic-Bold.woff2";
import FSAlbertArabic_ExtraBold_Woff2 from "~/assets/fonts/FSAlbertArabic-ExtraBold.woff2";
import FSAlbertArabic_Light_Woff2 from "~/assets/fonts/FSAlbertArabic-Light.woff2";
import FSAlbertArabic_Regular_Woff2 from "~/assets/fonts/FSAlbertArabic-Regular.woff2";
import FSAlbertArabic_Thin_Woff2 from "~/assets/fonts/FSAlbertArabic-Thin.woff2";
import IBMPlexSansArabic_ExtraBold_ttf from "~/assets/fonts/IBMPlexSansArabic-Bold.ttf";
import IBMPlexSansArabic_Light_ttf from "~/assets/fonts/IBMPlexSansArabic-ExtraLight.ttf";
import IBMPlexSansArabic_Regular_ttf from "~/assets/fonts/IBMPlexSansArabic-Regular.ttf";
import IBMPlexSansArabic_Bold_ttf from "~/assets/fonts/IBMPlexSansArabic-SemiBold.ttf";
import IBMPlexSansArabic_Thin_ttf from "~/assets/fonts/IBMPlexSansArabic-Thin.ttf";

const fontFaces = [
  <Global
    key="thin"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontWeight: 100,
        src: `url("${FSAlbertArabic_Thin_Woff2}") format("woff2")`,
      },
    }}
  />,
  <Global
    key="light"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontStyle: "normal",
        fontWeight: 200,
        src: `url("${FSAlbertArabic_Light_Woff2}") format("woff2")`,
      },
    }}
  />,
  <Global
    key="regular"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontStyle: "normal",
        fontWeight: 400,
        src: `url("${FSAlbertArabic_Regular_Woff2}") format("woff2")`,
      },
    }}
  />,

  <Global
    key="bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontStyle: "normal",
        fontWeight: 700,
        src: `url("${FSAlbertArabic_Bold_Woff2}") format("woff2")`,
      },
    }}
  />,

  <Global
    key="extra-bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontStyle: "normal",
        fontWeight: 900,
        src: `url("${FSAlbertArabic_ExtraBold_Woff2}") format("woff2")`,
      },
    }}
  />,
];

const rebrandedFontFaces = [
  <Global
    key="thin"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "FS Albert Arabic",
        fontWeight: 100,
        src: `url("${IBMPlexSansArabic_Thin_ttf}") format("truetype")`,
      },
    }}
  />,
  <Global
    key="light"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 200,
        src: `url("${IBMPlexSansArabic_Light_ttf}") format("truetype")`,
      },
    }}
  />,
  <Global
    key="regular"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 400,
        src: `url("${IBMPlexSansArabic_Regular_ttf}") format("truetype")`,
      },
    }}
  />,

  <Global
    key="bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 700,
        src: `url("${IBMPlexSansArabic_Bold_ttf}") format("truetype")`,
      },
    }}
  />,

  <Global
    key="extra-bold"
    styles={{
      "@font-face": {
        fontDisplay: "swap",
        fontFamily: "IBM Plex Sans Arabic",
        fontStyle: "normal",
        fontWeight: 900,
        src: `url("${IBMPlexSansArabic_ExtraBold_ttf}") format("truetype")`,
      },
    }}
  />,
];

// const roshnDefaultStyles = (
// 	<Global
// 		styles={(theme: AppTheme) => [
// 			{
// 				[`*:focus-visible`]: {
// 					outline: "none",
// 				},

// 				body: {
// 					fontFamily: theme.rds.font,
// 					...theme.rds.typography.bodyM,
// 				},

// 				"body > #root": {
// 					position: "relative",
// 					zIndex: theme.zIndices.root,
// 				},
// 			},
// 		]}
// 	/>
// );

/**
 * Provides baseline styles for the application.
 * - Resets browser styles
 * - Provides font faces
 * - Provides default styles
 */
export const CssBaseline = () => {
  return (
    <>
      {/* {normalize} */}
      {fontFaces}
      {rebrandedFontFaces}
      {/* {roshnDefaultStyles} */}
    </>
  );
};
